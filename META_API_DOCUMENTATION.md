# Meta (Facebook/Instagram) Analytics API Documentation

This document describes the complete Meta integration API for your social media analytics tool.

## Overview

The Meta integration allows brands to connect their Facebook and Instagram accounts to fetch analytics data, posts, insights, and manage connections.

## Authentication

All API endpoints require authentication using Laravel Sanctum tokens and appropriate user roles (admin, super_admin).

## Base URL
```
https://meta.invopix.com/api/meta
```

## Connection Flow

### 1. Initiate Meta Connection

**Endpoint:** `POST /api/meta/connect`

**Description:** Initiates the Meta OAuth flow for a specific brand.

**Request:**
```json
{
  "brand_id": 1
}
```

**Response:**
```json
{
  "status": "success",
  "redirect_url": "https://www.facebook.com/v23.0/dialog/oauth?client_id=..."
}
```

**Usage:** Redirect the user to the `redirect_url` to complete OAuth.

### 2. OAuth Callback (Automatic)

**Endpoint:** `GET /meta/callback`

**Description:** Handles the Meta OAuth callback automatically. Users are redirected here after authorizing your app.

**Redirect:** After successful connection, users are redirected to `/dashboard/home` with success message.

## Connection Management

### 3. Get Brand Connection Details

**Endpoint:** `GET /api/meta/connection`

**Parameters:**
- `brand_id` (required): Brand ID

**Response:**
```json
{
  "status": "success",
  "data": {
    "connection": {
      "id": 1,
      "brand_id": 1,
      "meta_user_name": "John Doe",
      "meta_user_email": "<EMAIL>",
      "connection_status": "active",
      "token_expires_at": "2025-04-01T00:00:00Z",
      "last_sync_at": "2025-02-01T12:00:00Z"
    },
    "facebook_pages": [
      {
        "id": "*********",
        "name": "My Business Page",
        "category": "Business",
        "fan_count": 1500,
        "access_token": "page_token_here"
      }
    ],
    "instagram_accounts": [
      {
        "id": "*********",
        "username": "mybusiness",
        "name": "My Business",
        "followers_count": 2500,
        "connected_facebook_page": "*********"
      }
    ],
    "is_token_valid": true
  }
}
```

### 4. List All Brand Connections

**Endpoint:** `GET /api/meta/connections`

**Parameters:**
- `brand_id` (required): Brand ID

**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "meta_user_name": "John Doe",
      "meta_user_email": "<EMAIL>",
      "connection_status": "active",
      "facebook_pages_count": 2,
      "instagram_accounts_count": 1,
      "last_sync_at": "2025-02-01T12:00:00Z",
      "token_expires_at": "2025-04-01T00:00:00Z",
      "is_token_expired": false,
      "is_active": true,
      "connected_by": {
        "id": 1,
        "name": "Admin User",
        "email": "<EMAIL>"
      },
      "created_at": "2025-01-15T10:00:00Z"
    }
  ]
}
```

### 5. Disconnect Meta Connection

**Endpoint:** `POST /api/meta/disconnect`

**Request:**
```json
{
  "brand_id": 1,
  "connection_id": 1
}
```

**Response:**
```json
{
  "status": "success",
  "message": "Meta connection disconnected successfully"
}
```

### 6. Check Connection Health

**Endpoint:** `POST /api/meta/health-check`

**Request:**
```json
{
  "brand_id": 1,
  "connection_id": 1
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "is_valid": true,
    "connection_status": "active",
    "token_expires_at": "2025-04-01T00:00:00Z",
    "last_sync_at": "2025-02-01T12:00:00Z",
    "facebook_pages_count": 2,
    "instagram_accounts_count": 1
  }
}
```

### 7. Refresh Connection Data

**Endpoint:** `POST /api/meta/refresh-data`

**Description:** Refreshes the list of Facebook pages and Instagram accounts.

**Request:**
```json
{
  "brand_id": 1,
  "connection_id": 1
}
```

**Response:**
```json
{
  "status": "success",
  "message": "Connection data refreshed successfully",
  "data": {
    "facebook_pages": [...],
    "instagram_accounts": [...],
    "facebook_pages_count": 2,
    "instagram_accounts_count": 1
  }
}
```

## Facebook Data Retrieval

### 8. Get Facebook Page Insights

**Endpoint:** `GET /api/meta/facebook/page/insights`

**Parameters:**
- `brand_id` (required): Brand ID
- `page_id` (required): Facebook Page ID
- `metrics` (optional): Array of specific metrics to fetch
- `period` (optional): 'day', 'week', 'days_28' (default: 'day')
- `days` (optional): Number of days to fetch (1-90, default: 30)

**Example Request:**
```
GET /api/meta/facebook/page/insights?brand_id=1&page_id=*********&period=day&days=7
```

**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "name": "page_impressions",
      "period": "day",
      "values": [
        {
          "value": 1250,
          "end_time": "2025-02-01T08:00:00+0000"
        }
      ]
    }
  ]
}
```

### 9. Get Facebook Page Posts

**Endpoint:** `GET /api/meta/facebook/page/posts`

**Parameters:**
- `brand_id` (required): Brand ID
- `page_id` (required): Facebook Page ID
- `limit` (optional): Number of posts to fetch (1-100, default: 25)

**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "id": "*********_*********",
      "message": "Check out our latest product!",
      "created_time": "2025-02-01T10:00:00+0000",
      "type": "photo",
      "permalink_url": "https://facebook.com/...",
      "reactions": {
        "summary": {
          "total_count": 45
        }
      },
      "comments": {
        "summary": {
          "total_count": 12
        }
      },
      "shares": {
        "count": 8
      }
    }
  ]
}
```

## Instagram Data Retrieval

### 10. Get Instagram Account Insights

**Endpoint:** `GET /api/meta/instagram/insights`

**Parameters:**
- `brand_id` (required): Brand ID
- `account_id` (required): Instagram Business Account ID
- `metrics` (optional): Array of specific metrics to fetch
- `period` (optional): 'day', 'week', 'days_28' (default: 'day')
- `days` (optional): Number of days to fetch (1-90, default: 30)

**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "name": "impressions",
      "period": "day",
      "values": [
        {
          "value": 2500,
          "end_time": "2025-02-01T08:00:00+0000"
        }
      ]
    }
  ]
}
```

### 11. Get Instagram Media Posts

**Endpoint:** `GET /api/meta/instagram/media`

**Parameters:**
- `brand_id` (required): Brand ID
- `account_id` (required): Instagram Business Account ID
- `limit` (optional): Number of media items to fetch (1-100, default: 25)

**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "id": "1784*********0123",
      "caption": "Beautiful sunset today! #nature",
      "media_type": "IMAGE",
      "media_url": "https://scontent.cdninstagram.com/...",
      "permalink": "https://www.instagram.com/p/...",
      "timestamp": "2025-02-01T18:30:00+0000",
      "like_count": 156,
      "comments_count": 23
    }
  ]
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "status": "error",
  "message": "Error description",
  "errors": {
    "field_name": ["Validation error message"]
  }
}
```

## Available Metrics

### Facebook Page Metrics:
- `page_impressions`
- `page_reach`
- `page_engaged_users`
- `page_post_engagements`
- `page_fans`
- `page_fan_adds`
- `page_fan_removes`

### Instagram Account Metrics:
- `impressions`
- `reach`
- `profile_views`
- `website_clicks`
- `follower_count`

## Usage Examples

### Frontend Integration Example (JavaScript)

```javascript
// Connect brand to Meta
async function connectBrandToMeta(brandId) {
  const response = await fetch('/api/meta/connect', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify({ brand_id: brandId })
  });
  
  const data = await response.json();
  if (data.status === 'success') {
    window.location.href = data.redirect_url;
  }
}

// Get page insights
async function getPageInsights(brandId, pageId) {
  const response = await fetch(`/api/meta/facebook/page/insights?brand_id=${brandId}&page_id=${pageId}&days=30`, {
    headers: {
      'Authorization': 'Bearer ' + token
    }
  });
  
  return await response.json();
}
```

## Database Schema

The system uses the `brand_meta_connections` table to store connection data:

- `brand_id`: Links to brands table
- `user_id`: User who created the connection
- `meta_user_id`: Meta user ID
- `access_token`: Encrypted access token
- `token_expires_at`: Token expiration date
- `facebook_pages`: JSON array of connected pages
- `instagram_accounts`: JSON array of connected accounts
- `connection_status`: active, expired, revoked, error
- `last_sync_at`: Last data synchronization timestamp

## Security Notes

1. Access tokens are stored securely and not exposed in API responses
2. All endpoints require proper authentication
3. Brand access is validated for each request
4. Tokens are automatically refreshed when possible
5. Connection health is monitored and reported
