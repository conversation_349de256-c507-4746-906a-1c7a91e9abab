<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Auth;

class MetaController extends Controller
{
    // Step 1: Redirect to Meta
    public function redirectToMeta()
    {
        $appId = config('services.meta.app_id');
        $redirectUri = config('services.meta.redirect_uri');
        $scopes = config('services.meta.scopes');

        //  dd($redirectUri);
        $url = "https://www.facebook.com/v23.0/dialog/oauth?client_id={$appId}&redirect_uri={$redirectUri}&scope={$scopes}";

        return redirect($url);
    }

    // Step 2: Handle callback and exchange code for token
    public function handleCallback(Request $request)
    {
       
        if (!$request->has('code')) {
            return redirect()->route('dashboard')->with('error', 'Authorization failed');
        }

        $appId = config('services.meta.app_id');
        $appSecret = config('services.meta.app_secret');
        $redirectUri = config('services.meta.redirect_uri');

        // Exchange code for short-lived token
        $response = Http::get('https://graph.facebook.com/v23.0/oauth/access_token', [
            'client_id' => $appId,
            'redirect_uri' => $redirectUri,
            'client_secret' => $appSecret,
            'code' => $request->code,
        ]);

        $data = $response->json();

        if (!isset($data['access_token'])) {
            return redirect()->route('dashboard')->with('error', 'Failed to retrieve access token.');
        }

        $shortLivedToken = $data['access_token'];

        // Exchange short-lived token for long-lived token
        $longLived = Http::get('https://graph.facebook.com/v23.0/oauth/access_token', [
            'grant_type' => 'fb_exchange_token',
            'client_id' => $appId,
            'client_secret' => $appSecret,
            'fb_exchange_token' => $shortLivedToken,
        ])->json();

        $accessToken = $longLived['access_token'] ?? $shortLivedToken;
        $expiresAt = now()->addSeconds($longLived['expires_in'] ?? 3600);

      

        return redirect()->route('dashboard')->with('success', 'Meta account connected successfully!');
    }
}
