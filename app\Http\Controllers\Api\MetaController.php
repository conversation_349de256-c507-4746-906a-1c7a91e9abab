<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Services\MetaService;
use App\Models\BrandMetaConnection;
use App\Models\Brands;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class MetaController extends Controller
{
    protected $metaService;

    public function __construct(MetaService $metaService)
    {
        $this->metaService = $metaService;
    }

    /**
     * Redirect to Meta OAuth with brand context
     */
    public function redirectToMeta(Request $request) {
        $validator = Validator::make($request->all(), [
            'brand_id' => 'required|exists:brands,id'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $brandId = $request->brand_id;
        $appId = config('services.meta.app_id');
        $redirectUri = config('services.meta.redirect_uri');
        $scopes = config('services.meta.scopes');

        // Store brand_id in session for callback
        session(['meta_brand_id' => $brandId]);

        $url = "https://www.facebook.com/v23.0/dialog/oauth?client_id={$appId}&redirect_uri={$redirectUri}&scope={$scopes}&state=" . csrf_token();

        return response()->json([
            'status' => 'success',
            'redirect_url' => $url
        ]);
    }

    /**
     * Handle Meta OAuth callback and store connection
     */
    public function handleCallback(Request $request)
    {
        try {
            if (!$request->has('code')) {
                return redirect()->route('dashboard.home')->with('error', 'Meta authorization was cancelled or failed.');
            }

            $brandId = session('meta_brand_id');
            if (!$brandId) {
                return redirect()->route('dashboard.home')->with('error', 'Invalid session. Please try connecting again.');
            }

            // Verify brand exists and user has access
            $brand = Brands::find($brandId);
            if (!$brand) {
                return redirect()->route('dashboard.home')->with('error', 'Brand not found.');
            }

            $user = Auth::user();

            // Exchange code for token
            $tokenData = $this->metaService->exchangeCodeForToken($request->code);
            $accessToken = $tokenData['access_token'];
            $expiresIn = $tokenData['expires_in'];

            // Get user info from Meta
            $userInfo = $this->metaService->getUserInfo($accessToken);

            // Get user's Facebook pages
            $pages = $this->metaService->getUserPages($accessToken);

            // Get Instagram accounts
            $instagramAccounts = $this->metaService->getInstagramAccounts($accessToken, $pages);

            // Check if connection already exists for this brand and Meta user
            $existingConnection = BrandMetaConnection::where('brand_id', $brandId)
                ->where('meta_user_id', $userInfo['id'])
                ->first();

            $connectionData = [
                'brand_id' => $brandId,
                'user_id' => $user->id,
                'meta_user_id' => $userInfo['id'],
                'meta_user_name' => $userInfo['name'],
                'meta_user_email' => $userInfo['email'] ?? null,
                'access_token' => $accessToken,
                'token_expires_at' => Carbon::now()->addSeconds($expiresIn),
                'granted_scopes' => explode(',', config('services.meta.scopes')),
                'facebook_pages' => $pages,
                'instagram_accounts' => $instagramAccounts,
                'connection_status' => 'active',
                'last_sync_at' => now(),
            ];

            // Set primary accounts if available
            if (!empty($pages)) {
                $connectionData['primary_facebook_page_id'] = $pages[0]['id'];
            }
            if (!empty($instagramAccounts)) {
                $connectionData['primary_instagram_account_id'] = $instagramAccounts[0]['id'];
            }

            if ($existingConnection) {
                $existingConnection->update($connectionData);
                $connection = $existingConnection;
            } else {
                $connection = BrandMetaConnection::create($connectionData);
            }

            // Clear session
            session()->forget('meta_brand_id');

            return redirect()->route('dashboard.home')->with('success', 'Meta account connected successfully! Found ' . count($pages) . ' Facebook pages and ' . count($instagramAccounts) . ' Instagram accounts.');

        } catch (\Exception $e) {
            Log::error('Meta callback error: ' . $e->getMessage());
            return redirect()->route('dashboard.home')->with('error', 'Failed to connect Meta account: ' . $e->getMessage());
        }
    }

    /**
     * Get brand's Meta connection details
     */
    public function getBrandConnection(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'brand_id' => 'required|exists:brands,id'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $brandId = $request->brand_id;
        $connection = BrandMetaConnection::where('brand_id', $brandId)
            ->active()
            ->with('brand', 'user')
            ->first();

        if (!$connection) {
            return response()->json([
                'status' => 'error',
                'message' => 'No active Meta connection found for this brand'
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'data' => [
                'connection' => $connection->makeVisible(['access_token']),
                'facebook_pages' => $connection->facebook_pages,
                'instagram_accounts' => $connection->instagram_accounts,
                'is_token_valid' => $this->metaService->validateToken($connection->access_token)
            ]
        ]);
    }

    /**
     * Get Facebook page insights
     */
    public function getPageInsights(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'brand_id' => 'required|exists:brands,id',
            'page_id' => 'required|string',
            'metrics' => 'array',
            'period' => 'in:day,week,days_28',
            'days' => 'integer|min:1|max:90'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $connection = BrandMetaConnection::where('brand_id', $request->brand_id)
                ->active()
                ->first();

            if (!$connection) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No active Meta connection found'
                ], 404);
            }

            // Find the page and get its access token
            $pageAccessToken = null;
            foreach ($connection->facebook_pages as $page) {
                if ($page['id'] === $request->page_id) {
                    $pageAccessToken = $page['access_token'];
                    break;
                }
            }

            if (!$pageAccessToken) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Page not found in connected pages'
                ], 404);
            }

            $insights = $this->metaService->getPageInsights(
                $request->page_id,
                $pageAccessToken,
                $request->metrics ?? [],
                $request->period ?? 'day',
                $request->days ?? 30
            );

            $connection->updateLastSync();

            return response()->json([
                'status' => 'success',
                'data' => $insights
            ]);

        } catch (\Exception $e) {
            Log::error('Page insights error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch page insights: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get Instagram account insights
     */
    public function getInstagramInsights(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'brand_id' => 'required|exists:brands,id',
            'account_id' => 'required|string',
            'metrics' => 'array',
            'period' => 'in:day,week,days_28',
            'days' => 'integer|min:1|max:90'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $connection = BrandMetaConnection::where('brand_id', $request->brand_id)
                ->active()
                ->first();

            if (!$connection) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No active Meta connection found'
                ], 404);
            }

            // Find the Instagram account and get its access token
            $accessToken = null;
            foreach ($connection->instagram_accounts as $account) {
                if ($account['id'] === $request->account_id) {
                    $accessToken = $account['page_access_token'];
                    break;
                }
            }

            if (!$accessToken) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Instagram account not found in connected accounts'
                ], 404);
            }

            $insights = $this->metaService->getInstagramInsights(
                $request->account_id,
                $accessToken,
                $request->metrics ?? [],
                $request->period ?? 'day',
                $request->days ?? 30
            );

            $connection->updateLastSync();

            return response()->json([
                'status' => 'success',
                'data' => $insights
            ]);

        } catch (\Exception $e) {
            Log::error('Instagram insights error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch Instagram insights: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get Facebook page posts
     */
    public function getPagePosts(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'brand_id' => 'required|exists:brands,id',
            'page_id' => 'required|string',
            'limit' => 'integer|min:1|max:100'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $connection = BrandMetaConnection::where('brand_id', $request->brand_id)
                ->active()
                ->first();

            if (!$connection) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No active Meta connection found'
                ], 404);
            }

            // Find the page and get its access token
            $pageAccessToken = null;
            foreach ($connection->facebook_pages as $page) {
                if ($page['id'] === $request->page_id) {
                    $pageAccessToken = $page['access_token'];
                    break;
                }
            }

            if (!$pageAccessToken) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Page not found in connected pages'
                ], 404);
            }

            $posts = $this->metaService->getPagePosts(
                $request->page_id,
                $pageAccessToken,
                $request->limit ?? 25
            );

            $connection->updateLastSync();

            return response()->json([
                'status' => 'success',
                'data' => $posts
            ]);

        } catch (\Exception $e) {
            Log::error('Page posts error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch page posts: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get Instagram media posts
     */
    public function getInstagramMedia(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'brand_id' => 'required|exists:brands,id',
            'account_id' => 'required|string',
            'limit' => 'integer|min:1|max:100'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $connection = BrandMetaConnection::where('brand_id', $request->brand_id)
                ->active()
                ->first();

            if (!$connection) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No active Meta connection found'
                ], 404);
            }

            // Find the Instagram account and get its access token
            $accessToken = null;
            foreach ($connection->instagram_accounts as $account) {
                if ($account['id'] === $request->account_id) {
                    $accessToken = $account['page_access_token'];
                    break;
                }
            }

            if (!$accessToken) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Instagram account not found in connected accounts'
                ], 404);
            }

            $media = $this->metaService->getInstagramMedia(
                $request->account_id,
                $accessToken,
                $request->limit ?? 25
            );

            $connection->updateLastSync();

            return response()->json([
                'status' => 'success',
                'data' => $media
            ]);

        } catch (\Exception $e) {
            Log::error('Instagram media error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch Instagram media: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * List all Meta connections for a brand
     */
    public function listBrandConnections(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'brand_id' => 'required|exists:brands,id'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $connections = BrandMetaConnection::where('brand_id', $request->brand_id)
            ->with('user:id,name,email')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'status' => 'success',
            'data' => $connections->map(function ($connection) {
                return [
                    'id' => $connection->id,
                    'meta_user_name' => $connection->meta_user_name,
                    'meta_user_email' => $connection->meta_user_email,
                    'connection_status' => $connection->connection_status,
                    'facebook_pages_count' => count($connection->facebook_pages ?? []),
                    'instagram_accounts_count' => count($connection->instagram_accounts ?? []),
                    'last_sync_at' => $connection->last_sync_at,
                    'token_expires_at' => $connection->token_expires_at,
                    'is_token_expired' => $connection->isTokenExpired(),
                    'is_active' => $connection->isActive(),
                    'connected_by' => $connection->user,
                    'created_at' => $connection->created_at,
                ];
            })
        ]);
    }

    /**
     * Disconnect Meta connection
     */
    public function disconnectBrand(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'brand_id' => 'required|exists:brands,id',
            'connection_id' => 'required|exists:brand_meta_connections,id'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $connection = BrandMetaConnection::where('id', $request->connection_id)
            ->where('brand_id', $request->brand_id)
            ->first();

        if (!$connection) {
            return response()->json([
                'status' => 'error',
                'message' => 'Connection not found'
            ], 404);
        }

        $connection->markAsRevoked();

        return response()->json([
            'status' => 'success',
            'message' => 'Meta connection disconnected successfully'
        ]);
    }

    /**
     * Check connection health and refresh token if needed
     */
    public function checkConnectionHealth(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'brand_id' => 'required|exists:brands,id',
            'connection_id' => 'required|exists:brand_meta_connections,id'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $connection = BrandMetaConnection::where('id', $request->connection_id)
                ->where('brand_id', $request->brand_id)
                ->first();

            if (!$connection) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Connection not found'
                ], 404);
            }

            $isValid = $this->metaService->validateToken($connection->access_token);

            if (!$isValid) {
                // Try to refresh the token
                $refreshResult = $this->metaService->refreshLongLivedToken($connection->access_token);

                if ($refreshResult) {
                    $connection->update([
                        'access_token' => $refreshResult['access_token'],
                        'token_expires_at' => Carbon::now()->addSeconds($refreshResult['expires_in']),
                        'connection_status' => 'active'
                    ]);
                    $isValid = true;
                } else {
                    $connection->markAsExpired();
                }
            }

            return response()->json([
                'status' => 'success',
                'data' => [
                    'is_valid' => $isValid,
                    'connection_status' => $connection->connection_status,
                    'token_expires_at' => $connection->token_expires_at,
                    'last_sync_at' => $connection->last_sync_at,
                    'facebook_pages_count' => count($connection->facebook_pages ?? []),
                    'instagram_accounts_count' => count($connection->instagram_accounts ?? [])
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Connection health check error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to check connection health: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Refresh connection data (pages and Instagram accounts)
     */
    public function refreshConnectionData(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'brand_id' => 'required|exists:brands,id',
            'connection_id' => 'required|exists:brand_meta_connections,id'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $connection = BrandMetaConnection::where('id', $request->connection_id)
                ->where('brand_id', $request->brand_id)
                ->active()
                ->first();

            if (!$connection) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Active connection not found'
                ], 404);
            }

            // Refresh pages and Instagram accounts
            $pages = $this->metaService->getUserPages($connection->access_token);
            $instagramAccounts = $this->metaService->getInstagramAccounts($connection->access_token, $pages);

            $connection->update([
                'facebook_pages' => $pages,
                'instagram_accounts' => $instagramAccounts,
                'last_sync_at' => now()
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Connection data refreshed successfully',
                'data' => [
                    'facebook_pages' => $pages,
                    'instagram_accounts' => $instagramAccounts,
                    'facebook_pages_count' => count($pages),
                    'instagram_accounts_count' => count($instagramAccounts)
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Connection data refresh error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to refresh connection data: ' . $e->getMessage()
            ], 500);
        }
    }
}
