<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Businesses;
use App\Models\Roles;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class authController extends Controller
{

    public function register(Request $request)
    {

        // Validate the input data
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'email' => 'required|string|email|unique:users,email', // Unique check for email
            'business_name' => 'required|string|unique:businesses,business_name', // Unique check for business name
            'password' => 'required|string|confirmed',
        ]);

        // If validation fails, return errors
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }


          // Find the Business Owner role
        $role = Roles::where('role_key', 'admin')->first();

        // Check if the email already exists
       // $existingUser = User::where('role_id', $role->id)->where('email', $request->email)->first();
        $existingUser = User::where('email', $request->email)->first();

        if ($existingUser) {
            return response()->json(['error' => 'Email with admin role is already taken.'], 422);
        }

         // Create the business and link it to the user
        $businessSlug = Str::slug($request->business_name);
        $business = Businesses::create([
            'business_name' => $request->business_name,
            'slug' => $businessSlug,

            'business_logo' => json_encode([
                'logo' => '',
                'favicon' => ''
            ]),
           
        ]);

      

        // Optionally regenerate token for security
        $newToken = Str::random(60);
        // Create the user
        $user = User::create([
            'name' => $request->name,
            'business_id' => $business->id,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role_id' => $role->id,
            'status' => 1,
            'remember_token' => $newToken,
        ]);


        Auth::login($user);

        return response()->json(["message" => "Successfully signed up", "status" => "success"]);
    }


    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|string|email',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        $credentials = $validator->validated();

        $user = User::where('email', $request->email)->firstOrFail();
        if (!$user) {
            return response()->json(['errors' => ['email' => ['User does not exist']]], 404);
        }
        if (!Auth::attempt($credentials)) {
            return response()->json(['errors' => ['password' => ['Incorrect password']]], 401);
        }

        // Handle invalid roles
        if ($user->status != 1) {
            Auth::logout(); // Log out the user if their role is not authorized
            return response()->json(['error' => 'Invalid user access'], 402);
        }

        // ✅ Restrict franchise users if franchisee status is 0
        // if (
        //     $user->role && $user->role->role_key === 'franchise' &&
        //     $user->franchisee && $user->franchisee->status == 0
        // ) {
        //     Auth::logout();
        //     return response()->json(['error' => 'Franchise account is inactive'], 402);
        // }

        $request->session()->regenerate();

        return response()->json([
            'message' => 'Login successful',
        ], 200);
    }

    public function updatePassword(Request $request)
    {

        $request->validate([
            'new_password' => 'required|min:6',
            'token' => 'required|exists:users,password_reset_token',
        ]);

        $user = User::where('password_reset_token', $request->token)->first();

        if (!$user) {
            return response()->json(['error' => 'Invalid token'], 401);
        }

        // Handle invalid roles
        if ($user->status != 1) {
            Auth::logout(); // Log out the user if their role is not authorized
            return response()->json(['error' => 'Invalid user access'], 402);
        }

        // Update Password
        $user->update([
            'password' => Hash::make($request->new_password),
            // 'password_reset_token' => null, // Clear token after use
        ]);

        Auth::logout();

        // Only invalidate and regenerate the session if it's available (web request)
        if ($request->hasSession()) {
            $request->session()->invalidate();
            $request->session()->regenerateToken();
        }

        return response()->json(['message' => 'Password reset successfull'], 200);
    }


    public function forgetPassword(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'email' => 'required|string|email',

        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = User::where('email', $request->email)->first();

        if (!$user) {
            return response()->json(['error' => 'User not found'], 404);
        }

        if ($user) {
            $resetToken = Str::random(60);
            $user->update([
                'password_reset_token' => $resetToken,
            ]);
          
            // Send Email to the New User
            sendForgetPasswordMail($user, 'user reset password', $resetToken);
        }


        return response()->json(['message' => 'Password reset email sent'], 200);
    }



    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return response()->json(['message' => 'Logged out successfully'], 200);
    }
}
