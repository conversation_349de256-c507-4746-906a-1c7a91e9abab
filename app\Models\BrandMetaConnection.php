<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class BrandMetaConnection extends Model
{
    use HasFactory;

    protected $table = 'brand_meta_connections';

    protected $fillable = [
        'brand_id',
        'user_id',
        'meta_user_id',
        'meta_user_name',
        'meta_user_email',
        'access_token',
        'token_expires_at',
        'refresh_token',
        'granted_scopes',
        'facebook_pages',
        'primary_facebook_page_id',
        'instagram_accounts',
        'primary_instagram_account_id',
        'connection_status',
        'last_sync_at',
        'last_error',
        'business_accounts',
        'ad_accounts',
    ];

    protected $casts = [
        'token_expires_at' => 'datetime',
        'last_sync_at' => 'datetime',
        'granted_scopes' => 'array',
        'facebook_pages' => 'array',
        'instagram_accounts' => 'array',
        'last_error' => 'array',
        'business_accounts' => 'array',
        'ad_accounts' => 'array',
    ];

    protected $hidden = [
        'access_token',
        'refresh_token',
    ];

    /**
     * Relationship with Brand model
     */
    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brands::class, 'brand_id');
    }

    /**
     * Relationship with User model
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Check if the access token is expired
     */
    public function isTokenExpired(): bool
    {
        if (!$this->token_expires_at) {
            return false;
        }
        
        return Carbon::now()->isAfter($this->token_expires_at);
    }

    /**
     * Check if the connection is active and valid
     */
    public function isActive(): bool
    {
        return $this->connection_status === 'active' && !$this->isTokenExpired();
    }

    /**
     * Get the primary Facebook page information
     */
    public function getPrimaryFacebookPage(): ?array
    {
        if (!$this->primary_facebook_page_id || !$this->facebook_pages) {
            return null;
        }

        foreach ($this->facebook_pages as $page) {
            if ($page['id'] === $this->primary_facebook_page_id) {
                return $page;
            }
        }

        return null;
    }

    /**
     * Get the primary Instagram account information
     */
    public function getPrimaryInstagramAccount(): ?array
    {
        if (!$this->primary_instagram_account_id || !$this->instagram_accounts) {
            return null;
        }

        foreach ($this->instagram_accounts as $account) {
            if ($account['id'] === $this->primary_instagram_account_id) {
                return $account;
            }
        }

        return null;
    }

    /**
     * Update the last sync timestamp
     */
    public function updateLastSync(): void
    {
        $this->update(['last_sync_at' => now()]);
    }

    /**
     * Mark connection as expired
     */
    public function markAsExpired(): void
    {
        $this->update(['connection_status' => 'expired']);
    }

    /**
     * Mark connection as revoked
     */
    public function markAsRevoked(): void
    {
        $this->update(['connection_status' => 'revoked']);
    }

    /**
     * Mark connection as error with details
     */
    public function markAsError(array $errorDetails): void
    {
        $this->update([
            'connection_status' => 'error',
            'last_error' => $errorDetails
        ]);
    }

    /**
     * Scope for active connections
     */
    public function scopeActive($query)
    {
        return $query->where('connection_status', 'active');
    }

    /**
     * Scope for expired connections
     */
    public function scopeExpired($query)
    {
        return $query->where('connection_status', 'expired')
                    ->orWhere('token_expires_at', '<', now());
    }
}
