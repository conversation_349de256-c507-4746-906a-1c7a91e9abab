<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Brands extends Model
{
    use HasFactory;

       // 🔽 Add this line to explicitly set the table name
    protected $table = 'brands';

  protected $fillable = [
        'id',
        'user_id',
        'brand_name',
        'brand_logo',
        'status',
        'created_at',
        'updated_at',
    ];
  

    public function users()
    {
        return $this->hasMany(User::class, 'brand_id');
    }
}
