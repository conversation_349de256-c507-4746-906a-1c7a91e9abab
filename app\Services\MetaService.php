<?php

namespace App\Services;

use App\Models\BrandMetaConnection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class MetaService
{
    private const GRAPH_API_VERSION = 'v23.0';
    private const GRAPH_API_BASE_URL = 'https://graph.facebook.com';

    /**
     * Exchange authorization code for access token
     */
    public function exchangeCodeForToken(string $code): array
    {
        $appId = config('services.meta.app_id');
        $appSecret = config('services.meta.app_secret');
        $redirectUri = config('services.meta.redirect_uri');

        try {
            // Exchange code for short-lived token
            $response = Http::get(self::GRAPH_API_BASE_URL . '/' . self::GRAPH_API_VERSION . '/oauth/access_token', [
                'client_id' => $appId,
                'redirect_uri' => $redirectUri,
                'client_secret' => $appSecret,
                'code' => $code,
            ]);

            $data = $response->json();

            if (!isset($data['access_token'])) {
                throw new \Exception('Failed to retrieve access token: ' . json_encode($data));
            }

            $shortLivedToken = $data['access_token'];

            // Exchange short-lived token for long-lived token
            $longLivedResponse = Http::get(self::GRAPH_API_BASE_URL . '/' . self::GRAPH_API_VERSION . '/oauth/access_token', [
                'grant_type' => 'fb_exchange_token',
                'client_id' => $appId,
                'client_secret' => $appSecret,
                'fb_exchange_token' => $shortLivedToken,
            ]);

            $longLivedData = $longLivedResponse->json();

            return [
                'access_token' => $longLivedData['access_token'] ?? $shortLivedToken,
                'expires_in' => $longLivedData['expires_in'] ?? 3600,
                'token_type' => $longLivedData['token_type'] ?? 'bearer',
            ];
        } catch (\Exception $e) {
            Log::error('Meta token exchange failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get user information from Meta
     */
    public function getUserInfo(string $accessToken): array
    {
        try {
            $response = Http::get(self::GRAPH_API_BASE_URL . '/' . self::GRAPH_API_VERSION . '/me', [
                'access_token' => $accessToken,
                'fields' => 'id,name,email,picture'
            ]);

            $data = $response->json();

            if (isset($data['error'])) {
                throw new \Exception('Failed to get user info: ' . $data['error']['message']);
            }

            return $data;
        } catch (\Exception $e) {
            Log::error('Meta user info fetch failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get user's Facebook pages
     */
    public function getUserPages(string $accessToken): array
    {
        try {
            $response = Http::get(self::GRAPH_API_BASE_URL . '/' . self::GRAPH_API_VERSION . '/me/accounts', [
                'access_token' => $accessToken,
                'fields' => 'id,name,category,access_token,picture,fan_count,followers_count'
            ]);

            $data = $response->json();

            if (isset($data['error'])) {
                throw new \Exception('Failed to get pages: ' . $data['error']['message']);
            }

            return $data['data'] ?? [];
        } catch (\Exception $e) {
            Log::error('Meta pages fetch failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get Instagram business accounts connected to Facebook pages
     */
    public function getInstagramAccounts(string $accessToken, array $pages): array
    {
        $instagramAccounts = [];

        try {
            foreach ($pages as $page) {
                $pageAccessToken = $page['access_token'];
                
                $response = Http::get(self::GRAPH_API_BASE_URL . '/' . self::GRAPH_API_VERSION . '/' . $page['id'], [
                    'access_token' => $pageAccessToken,
                    'fields' => 'instagram_business_account'
                ]);

                $data = $response->json();

                if (isset($data['instagram_business_account'])) {
                    $igAccountId = $data['instagram_business_account']['id'];
                    
                    // Get Instagram account details
                    $igResponse = Http::get(self::GRAPH_API_BASE_URL . '/' . self::GRAPH_API_VERSION . '/' . $igAccountId, [
                        'access_token' => $pageAccessToken,
                        'fields' => 'id,username,name,profile_picture_url,followers_count,media_count,biography'
                    ]);

                    $igData = $igResponse->json();
                    
                    if (!isset($igData['error'])) {
                        $igData['connected_facebook_page'] = $page['id'];
                        $igData['page_access_token'] = $pageAccessToken;
                        $instagramAccounts[] = $igData;
                    }
                }
            }

            return $instagramAccounts;
        } catch (\Exception $e) {
            Log::error('Instagram accounts fetch failed: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get Facebook page insights
     */
    public function getPageInsights(string $pageId, string $pageAccessToken, array $metrics = [], string $period = 'day', int $days = 30): array
    {
        try {
            $defaultMetrics = [
                'page_impressions',
                'page_reach',
                'page_engaged_users',
                'page_post_engagements',
                'page_fans',
                'page_fan_adds',
                'page_fan_removes'
            ];

            $metricsToFetch = empty($metrics) ? $defaultMetrics : $metrics;
            $since = Carbon::now()->subDays($days)->format('Y-m-d');
            $until = Carbon::now()->format('Y-m-d');

            $response = Http::get(self::GRAPH_API_BASE_URL . '/' . self::GRAPH_API_VERSION . '/' . $pageId . '/insights', [
                'access_token' => $pageAccessToken,
                'metric' => implode(',', $metricsToFetch),
                'period' => $period,
                'since' => $since,
                'until' => $until
            ]);

            $data = $response->json();

            if (isset($data['error'])) {
                throw new \Exception('Failed to get page insights: ' . $data['error']['message']);
            }

            return $data['data'] ?? [];
        } catch (\Exception $e) {
            Log::error('Facebook page insights fetch failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get Instagram account insights
     */
    public function getInstagramInsights(string $accountId, string $accessToken, array $metrics = [], string $period = 'day', int $days = 30): array
    {
        try {
            $defaultMetrics = [
                'impressions',
                'reach',
                'profile_views',
                'website_clicks',
                'follower_count'
            ];

            $metricsToFetch = empty($metrics) ? $defaultMetrics : $metrics;
            $since = Carbon::now()->subDays($days)->timestamp;
            $until = Carbon::now()->timestamp;

            $response = Http::get(self::GRAPH_API_BASE_URL . '/' . self::GRAPH_API_VERSION . '/' . $accountId . '/insights', [
                'access_token' => $accessToken,
                'metric' => implode(',', $metricsToFetch),
                'period' => $period,
                'since' => $since,
                'until' => $until
            ]);

            $data = $response->json();

            if (isset($data['error'])) {
                throw new \Exception('Failed to get Instagram insights: ' . $data['error']['message']);
            }

            return $data['data'] ?? [];
        } catch (\Exception $e) {
            Log::error('Instagram insights fetch failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get Facebook page posts
     */
    public function getPagePosts(string $pageId, string $pageAccessToken, int $limit = 25): array
    {
        try {
            $response = Http::get(self::GRAPH_API_BASE_URL . '/' . self::GRAPH_API_VERSION . '/' . $pageId . '/posts', [
                'access_token' => $pageAccessToken,
                'fields' => 'id,message,story,created_time,type,status_type,permalink_url,full_picture,reactions.summary(true),comments.summary(true),shares',
                'limit' => $limit
            ]);

            $data = $response->json();

            if (isset($data['error'])) {
                throw new \Exception('Failed to get page posts: ' . $data['error']['message']);
            }

            return $data['data'] ?? [];
        } catch (\Exception $e) {
            Log::error('Facebook page posts fetch failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get Instagram media posts
     */
    public function getInstagramMedia(string $accountId, string $accessToken, int $limit = 25): array
    {
        try {
            $response = Http::get(self::GRAPH_API_BASE_URL . '/' . self::GRAPH_API_VERSION . '/' . $accountId . '/media', [
                'access_token' => $accessToken,
                'fields' => 'id,caption,media_type,media_url,permalink,thumbnail_url,timestamp,like_count,comments_count',
                'limit' => $limit
            ]);

            $data = $response->json();

            if (isset($data['error'])) {
                throw new \Exception('Failed to get Instagram media: ' . $data['error']['message']);
            }

            return $data['data'] ?? [];
        } catch (\Exception $e) {
            Log::error('Instagram media fetch failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Validate access token
     */
    public function validateToken(string $accessToken): bool
    {
        try {
            $response = Http::get(self::GRAPH_API_BASE_URL . '/' . self::GRAPH_API_VERSION . '/me', [
                'access_token' => $accessToken,
                'fields' => 'id'
            ]);

            $data = $response->json();
            
            return !isset($data['error']);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Refresh long-lived access token
     */
    public function refreshLongLivedToken(string $accessToken): ?array
    {
        try {
            $appId = config('services.meta.app_id');
            $appSecret = config('services.meta.app_secret');

            $response = Http::get(self::GRAPH_API_BASE_URL . '/' . self::GRAPH_API_VERSION . '/oauth/access_token', [
                'grant_type' => 'fb_exchange_token',
                'client_id' => $appId,
                'client_secret' => $appSecret,
                'fb_exchange_token' => $accessToken,
            ]);

            $data = $response->json();

            if (isset($data['error'])) {
                return null;
            }

            return [
                'access_token' => $data['access_token'],
                'expires_in' => $data['expires_in'] ?? 5184000, // 60 days default
            ];
        } catch (\Exception $e) {
            Log::error('Token refresh failed: ' . $e->getMessage());
            return null;
        }
    }
}
