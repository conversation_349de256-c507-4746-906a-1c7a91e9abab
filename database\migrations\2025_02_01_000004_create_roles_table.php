<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('roles', function (Blueprint $table) {
            $table->id();
            $table->longText('role'); // Long text for role name
            $table->longText('role_key'); // Long text for role key
            $table->timestamps(); // Adds created_at and updated_at fields
        });

        // Insert default roles if they don't exist
        DB::table('roles')->insertOrIgnore([
            [
                'role' => 'Super Admin',
                'role_key' => 'super_admin',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'role' => 'Admin',
                'role_key' => 'admin',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            
            [
                'role' => 'User',
                'role_key' => 'user',
                'created_at' => now(),
                'updated_at' => now(),
            ],
          
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('roles');
    }
};
