<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('brand_meta_connections', function (Blueprint $table) {
            $table->id();
            $table->foreignId('brand_id')->constrained('brands')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            
            // Meta Account Information
            $table->string('meta_user_id')->nullable();
            $table->string('meta_user_name')->nullable();
            $table->string('meta_user_email')->nullable();
            
            // Access Token Information
            $table->text('access_token');
            $table->timestamp('token_expires_at')->nullable();
            $table->text('refresh_token')->nullable();
            $table->json('granted_scopes')->nullable();
            
            // Facebook Page Information
            $table->json('facebook_pages')->nullable(); // Store array of connected pages
            $table->string('primary_facebook_page_id')->nullable();
            
            // Instagram Business Account Information
            $table->json('instagram_accounts')->nullable(); // Store array of connected IG accounts
            $table->string('primary_instagram_account_id')->nullable();
            
            // Connection Status
            $table->enum('connection_status', ['active', 'expired', 'revoked', 'error'])->default('active');
            $table->timestamp('last_sync_at')->nullable();
            $table->json('last_error')->nullable();
            
            // Additional Meta Business Information
            $table->json('business_accounts')->nullable(); // Meta Business Manager accounts
            $table->json('ad_accounts')->nullable(); // Connected ad accounts
            
            $table->timestamps();
            
            // Indexes
            $table->index(['brand_id', 'connection_status']);
            $table->index('meta_user_id');
            $table->unique(['brand_id', 'meta_user_id']); // One connection per Meta user per brand
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('brand_meta_connections');
    }
};
