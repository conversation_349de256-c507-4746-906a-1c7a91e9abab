$(document).ready(function () {
    $("#logout_btn").click(function (e) {
        e.preventDefault(); // Prevent the default anchor tag behavior
        let baseURL = window.location.origin;
        let apiEndpoint = "/api/logout";
        let apiUrl = baseURL + apiEndpoint;
        let csrfToken = $('meta[name="csrf-token"]').attr("content"); // Get the CSRF token value

        $.ajax({
            url: apiUrl,
            type: "POST",
            headers: {
                "X-CSRF-TOKEN": csrfToken, // Pass the CSRF token in the headers
            },
            success: function (response) {
                showAlert("Logging out...", "success");
                setTimeout(function () {
                    window.location.href = "/portal/login";
                }, 1000);
            },
            error: function (xhr, status, error) {
                // Handle error
                console.error(xhr);
                showAlert("system error.", "danger");
            },
        });
    });

    //add business users
    $("#add_user").validate({
        rules: {
            name: {
                required: true,
            },

            email: {
                required: true,
            },

            password: {
                required: true,
            },
            role: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            let apiEndpoint = "/api/business/user/add";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);
            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("user added !", "success");
                    var offcanvas = bootstrap.Offcanvas.getInstance(
                        document.getElementById("addUser")
                    );
                    offcanvas.hide();
                    setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                },
                error: function (xhr, status, error) {
                    if (xhr.status === 422) {
                        form.reset();
                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "user already exists with the same email.",
                            "warning"
                        );
                    } else {
                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error.contact your support team !",
                            "danger"
                        );
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById("addUser")
                        );
                        offcanvas.hide();
                    }
                },
            });
        },
    });

    //delete business users
    var businessUserId = null;
    // Handle delete button click

    $(document).on("click", ".business_user_delete_btn", function () {
        businessUserId = $(this).data("id");
        $("#businessUserDeleteModal").modal("show");
    });
    // Handle delete confirmation in modal
    $("#businessUserDeleteModal .delete_record").on("click", function () {
        if (businessUserId) {
            let baseURL = window.location.origin;
            let apiEndpoint = `/api/business/user/delete/${businessUserId}`;
            let apiUrl = baseURL + apiEndpoint;
            $.ajax({
                url: apiUrl,
                type: "DELETE",
                headers: {
                    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                        "content"
                    ),
                },
                success: function (response) {
                    // Handle success
                    $("#businessUserDeleteModal").modal("hide");

                    var $row = $('a[data-id="' + businessUserId + '"]').closest(
                        "tr"
                    );
                    $row.append(
                        '<span class="Deleting_row"> Deleting...</span>'
                    );

                    setTimeout(function () {
                        $row.fadeOut(500, function () {
                            $(this).remove(); // Remove the row from the DOM
                        });
                        showAlert("User deleted successfully !", "success");
                    }, 1000); // Duration of the CSS transition
                },
                error: function (xhr) {
                    // Handle error
                    showAlert("There is a problem contact supprt !", "error");
                },
            });
        }
    });

    //update business users status
    document.addEventListener("change", function (event) {
        if (event.target.classList.contains("business_user_status")) {
            const businessUserId = event.target.getAttribute("data-id");
            const checked = event.target.checked;
            let baseURL = window.location.origin;
            let apiEndpoint = `/api/business/user/status/update/${businessUserId}`;
            let apiUrl = baseURL + apiEndpoint;

            // Send PUT request to update status using AJAX
            $.ajax({
                url: apiUrl,
                type: "PUT",
                headers: {
                    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                        "content"
                    ),
                },
                data: { status: checked },
                success: function (response) {
                    // Show success message or update UI as needed
                    showAlert("status updated !", "success");
                },
                error: function (xhr, status, error) {
                    console.error(xhr);
                    showAlert("There is a problem contact supprt !", "error");
                },
            });
        }
    });

    //update business users data

    $("#update_business_user").validate({
        rules: {
            name: {
                required: true,
            },

            email: {
                required: true,
            },
            role: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            let apiEndpoint = "/api/business/user/update";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);
            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("user updated !", "success");
                    setTimeout(function () {
                        window.location.href = "/dashboard/profile";
                    }, 2000);
                },
                error: function (xhr, status, error) {
                    if (xhr.status === 422) {
                        form.reset();
                        resetDropify();
                        $(".form_process_loader").addClass("d-none");
                        showAlert("user already exists.", "warning");
                        // setTimeout(function () {
                        //     window.location.href = "/dashboard/profile";
                        // }, 2000);
                    } else {
                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error.contact your support team !",
                            "danger"
                        );
                        // setTimeout(function () {
                        //     window.location.href = "/dashboard/profile";
                        // }, 2000);
                    }
                },
            });
        },
    });

    // upload website images

    $("#upload_web_image").validate({
        rules: {
            web_images: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            let apiEndpoint = "/api/dashboard/image/web";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);

            // Get the form element and target its loader
            var $form = $(form);
            var $formLoader = $form.find("#form_loader");
            $formLoader.removeClass("d-none"); // Show the loader

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    resetDropify();
                    $formLoader.addClass("d-none");

                    var anchorElement = $("<a>", {
                        href: response.location,
                        text: response.location,
                        target: "_blank",
                    });

                    $("#url_link small").html(anchorElement);

                    showAlert("file uploaded added !", "success");
                },
                error: function (xhr, status, error) {
                    form.reset();
                    resetDropify();
                    $formLoader.addClass("d-none");
                    if (xhr.status === 422) {
                        showAlert("system error", "warning");
                    } else {
                        showAlert(
                            "There is an error.contact your support team !",
                            "danger"
                        );
                    }
                },
            });
        },
    });

    // delete  images/files from gallery

    $(document).on("submit", ".delete_gallery_file", function (e) {
        e.preventDefault();
        if (!confirm("Are you sure you want to delete this image?")) {
            return; // If the user clicks "Cancel", exit the function
        }
        // Get the form and data
        var form = $(this);
        var formData = new FormData(form[0]);

        // Set API endpoint
        let baseURL = window.location.origin;
        let apiEndpoint = "/api/dashboard/file/delete";
        let apiUrl = baseURL + apiEndpoint;

        $.ajax({
            url: apiUrl,
            type: "POST",
            data: formData,
            processData: false, // Important! Don't process the data
            contentType: false, // Important! Set content type to false
            dataType: "json",
            success: function (response) {
                if (response.success) {
                    // Remove the image div from the DOM
                    form.closest(".gallery_single_file").remove();
                    showAlert("Image deleted successfully!", "success");
                } else {
                    showAlert(response.message, "warning");
                }
            },
            error: function (xhr, status, error) {
                showAlert("Error deleting the image. Try again.", "danger");
            },
        });
    });

    $("#web_settings").validate({
        rules: {},

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            let apiEndpoint = "/api/dashboard/website/settings";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);
            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",

                success: function (response) {
                    $(".form_process_loader").addClass("d-none");
                    showAlert("settings updated !", "success");
                    setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                },
                error: function (xhr, status, error) {
                    if (xhr.status === 422) {
                        $(".form_process_loader").addClass("d-none");
                        showAlert("No change in the form data.", "warning");
                        // setTimeout(function () {
                        //     window.location.reload();
                        // }, 2000);
                    } else {
                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error.contact your support team !",
                            "danger"
                        );
                        // setTimeout(function () {
                        //     window.location.reload();
                        // }, 2000);
                    }
                },
            });
        },
    });

   
  

    // get selected country states
    $("#country_filter").on("change", function () {
        var country_id = $(this).val();
        $(".state_filter .form_process_loader_two").removeClass("d-none");

        if (country_id) {
            let apiUrl = `${window.location.origin}/api/get-states/${country_id}`;

            $.ajax({
                url: apiUrl,
                type: "GET",
                dataType: "json",
                success: function (response) {
                    $(".state_filter .form_process_loader_two").addClass(
                        "d-none"
                    );

                    if (response.status === 200) {
                        let states = response.data;
                        let options = `<option selected disabled>Select State</option>`;

                        states.forEach(function (state) {
                            options += `<option value="${state.id}">${state.name}</option>`;
                        });

                        $("#state_filter").html(options);
                    } else {
                        $("#state_filter").html(
                            "<option selected disabled>No states found</option>"
                        );
                    }
                },
                error: function () {
                    $(".state_filter .form_process_loader_two").addClass(
                        "d-none"
                    );
                    $("#state_filter").html(
                        "<option selected disabled>Error loading states</option>"
                    );
                },
            });
        }
    });

    $("#add_brand").validate({
        rules: {
            brand_name: {
                required: true,
            },
            brand_logo: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            let apiEndpoint = "/api/brand/add";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);

            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("Brand Added!", "success");
                    setTimeout(function () {
                        window.location.href = "/dashboard/brands";
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");

                    $(".error_list").remove();

                    if (xhr.status == 422) {
                        showAlert("Check the error's", "danger");
                        var errors = xhr.responseJSON;
                        $.each(errors, function (key, value) {
                            showAlert(value, "danger");
                        });
                    } else if (xhr.status == 403) {
                        showAlert("Check the error's above", "danger");
                        var errors = xhr.responseJSON.errors;
                        $("#validation_error").addClass("error_listing");

                        $.each(errors, function (key, value) {
                            const errorDiv = $(
                                '<div class="error_list"></div>'
                            ).text(value);
                            $("#validation_error").append(errorDiv);
                        });
                    } else {
                        showAlert(
                            "System error please check later !",
                            "danger"
                        );
                    }
                },
            });
        },
    });

    $("#update_brand").validate({
        rules: {
           brand_name: {
                required: true,
            },
            brand_logo: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            var brandId = $("#brand_id").val(); // Get ID from a hidden field or data attribute
            let apiEndpoint = "/api/brand/update/" + brandId;
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);
            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("Brand Data updated !", "success");
                    setTimeout(function () {
                        window.location.href = "/dashboard/brands";
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");

                    $(".error_list").remove();

                    if (xhr.status == 403) {
                        showAlert("Check the error's above", "danger");
                        var errors = xhr.responseJSON.errors;
                        $("#validation_error").addClass("error_listing");

                        $.each(errors, function (key, value) {
                            const errorDiv = $(
                                '<div class="error_list"></div>'
                            ).text(value);
                            $("#validation_error").append(errorDiv);
                        });
                    } else {
                        showAlert(
                            "System error please check later !",
                            "danger"
                        );
                    }
                },
            });
        },
    });

    var brandId = null;
    // Handle delete button click
    $(document).on("click", ".brand_delete_btn", function () {
        brandId = $(this).data("id");
        $("#brandDeleteModal").modal("show");
    });
    // Handle delete confirmation in modal
    $("#brandDeleteModal .delete_record").on("click", function () {
        if (brandId) {
            let baseURL = window.location.origin;
            let apiEndpoint = `/api/brand/delete/${brandId}`;
            let apiUrl = baseURL + apiEndpoint;
            $.ajax({
                url: apiUrl,
                type: "DELETE",
                headers: {
                    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                        "content"
                    ),
                },
                success: function (response) {
                    // Handle success
                    $("#brandDeleteModal").modal("hide");

                    var $row = $('a[data-id="' + brandId + '"]').closest(
                        "tr"
                    );
                    $row.append(
                        '<span class="Deleting_row"> Deleting...</span>'
                    );

                    setTimeout(function () {
                        $row.fadeOut(500, function () {
                            $(this).remove(); // Remove the row from the DOM
                        });
                        showAlert(
                            "brand deleted successfully !",
                            "success"
                        );
                    }, 1000); // Duration of the CSS transition
                },
                error: function (xhr) {
                    // Handle error
                    showAlert("There is a problem contact supprt !", "error");
                },
            });
        }
    });

    let brandConfirmId = null;
    let brandStatus = null;

    $(document).on("click", ".brand_status_btn", function () {
        brandConfirmId = $(this).data("id");
        brandStatus = $(this).is(":checked") ? 1 : 0; // Get new status (1 or 0)
        $("#brandConfirmModal").modal("show");
    });

    // Handle delete confirmation in modal
    $("#brandConfirmModal .confirm_record").on("click", function () {
        if (brandConfirmId) {
            let baseURL = window.location.origin;
            let apiEndpoint = `/api/brand/status/update/${brandConfirmId}`;
            let apiUrl = baseURL + apiEndpoint;
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: {
                    status: brandStatus, // send status
                },
                headers: {
                    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                        "content"
                    ),
                },
                success: function (response) {
                    // Handle success
                    $("#brandConfirmModal").modal("hide");
                    showAlert("brand status updated.", "success");

                    setTimeout(function () {
                        window.location.reload();
                    }, 1000);
                },
                error: function (xhr) {
                    // Handle error
                    showAlert("There is a problem contact supprt !", "error");
                },
            });
        }
    });

    ////////////////////// project management js

    $("#add_project").validate({
        rules: {
            first_name: {
                required: true,
            },
            last_name: {
                required: true,
            },
            postcode: {
                required: true,
            },
            project_value: {
                required: true,
            },
            payment_type: {
                required: true,
            },
            project_start_date: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            let apiEndpoint = "/api/project/add";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);

            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("Project added !", "success");
                    setTimeout(function () {
                        window.location.href = "/dashboard/projects";
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");

                    $(".error_list").remove();
                    if (xhr.status == 403) {
                        showAlert("Check the error's above", "danger");
                        var errors = xhr.responseJSON.errors;
                        $("#validation_error").addClass("error_listing");

                        $.each(errors, function (key, value) {
                            const errorDiv = $(
                                '<div class="error_list"></div>'
                            ).text(value);
                            $("#validation_error").append(errorDiv);
                        });
                    } else {
                        showAlert(
                            "System error please check later !",
                            "danger"
                        );
                    }
                },
            });
        },
    });

    $("#update_project").validate({
        rules: {
            first_name: {
                required: true,
            },
            last_name: {
                required: true,
            },
            postcode: {
                required: true,
            },
            project_value: {
                required: true,
            },
            payment_type: {
                required: true,
            },
            project_start_date: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            var projectId = $("#project_id").val(); // Get ID from a hidden field or data attribute
            let apiEndpoint = "/api/project/update/" + projectId;
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);

            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("Project updated !", "success");
                    setTimeout(function () {
                        window.location.href = "/dashboard/projects";
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");

                    $(".error_list").remove();

                    if (xhr.status == 403) {
                        showAlert("Check the error's above", "danger");
                        var errors = xhr.responseJSON.errors;
                        $("#validation_error").addClass("error_listing");

                        $.each(errors, function (key, value) {
                            const errorDiv = $(
                                '<div class="error_list"></div>'
                            ).text(value);
                            $("#validation_error").append(errorDiv);
                        });
                    } else {
                        showAlert(
                            "System error please check later !",
                            "danger"
                        );
                    }
                },
            });
        },
    });

    var projectId = null;
    // Handle delete button click
    $(document).on("click", ".project_delete_btn", function () {
        projectId = $(this).data("id");
        $("#projectDeleteModal").modal("show");
    });
    // Handle delete confirmation in modal
    $("#projectDeleteModal .delete_record").on("click", function () {
        if (projectId) {
            let baseURL = window.location.origin;
            let apiEndpoint = `/api/project/delete/${projectId}`;
            let apiUrl = baseURL + apiEndpoint;
            $.ajax({
                url: apiUrl,
                type: "DELETE",
                headers: {
                    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                        "content"
                    ),
                },
                success: function (response) {
                    // Handle success
                    $("#projectDeleteModal").modal("hide");

                    var $row = $('a[data-id="' + projectId + '"]').closest(
                        "tr"
                    );
                    $row.append(
                        '<span class="Deleting_row"> Deleting...</span>'
                    );

                    setTimeout(function () {
                        $row.fadeOut(500, function () {
                            $(this).remove(); // Remove the row from the DOM
                        });
                        showAlert("Project deleted successfully !", "success");
                    }, 1000); // Duration of the CSS transition
                },
                error: function (xhr) {
                    // Handle error
                    showAlert("There is a problem contact supprt !", "error");
                },
            });
        }
    });

    let projectConfirmId = null;
    // Handle delete button click
    $(document).on("change", ".project_status_btn", function (e) {
        const $checkbox = $(this);
        projectConfirmId = $checkbox.data("id");

        // If checkbox is already checked (completed project)
        if ($checkbox.is(":checked")) {
            // Show confirmation modal
            $("#projectConfirmModal").modal("show");
        } else {
            // Prevent unchecking and show alert
            $checkbox.prop("checked", true); // re-check it
            showAlert(
                "This project is already completed and cannot be reverted.",
                "warning"
            );
        }
    });

    // Handle delete confirmation in modal
    $("#projectConfirmModal .confirm_record").on("click", function () {
        if (projectConfirmId) {
            let baseURL = window.location.origin;
            let apiEndpoint = `/api/project/status/update/${projectConfirmId}`;
            let apiUrl = baseURL + apiEndpoint;
            $.ajax({
                url: apiUrl,
                type: "POST",
                headers: {
                    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                        "content"
                    ),
                },
                success: function (response) {
                    // Handle success
                    $("#projectConfirmModal").modal("hide");
                    showAlert("Project Completed.", "success");

                    setTimeout(function () {
                        window.location.reload();
                    }, 1000);
                },
                error: function (xhr) {
                    // Handle error
                    showAlert("There is a problem contact supprt !", "error");
                },
            });
        }
    });

    ////////////////////// lead management js

    $("#add_lead").validate({
        rules: {
            first_name: {
                required: true,
            },

            address: {
                required: true,
            },
            phone: {
                required: true,
            },

            service: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            let apiEndpoint = "/api/lead/add";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);

            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("Lead added !", "success");
                    setTimeout(function () {
                        window.location.href = "/dashboard/leads";
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");

                    $(".error_list").remove();
                    if (xhr.status == 403) {
                        showAlert("Check the error's above", "danger");
                        var errors = xhr.responseJSON.errors;
                        $("#validation_error").addClass("error_listing");

                        $.each(errors, function (key, value) {
                            const errorDiv = $(
                                '<div class="error_list"></div>'
                            ).text(value);
                            $("#validation_error").append(errorDiv);
                        });
                    } else {
                        showAlert(
                            "System error please check later !",
                            "danger"
                        );
                    }
                },
            });
        },
    });

    $("#update_lead").validate({
        rules: {
            first_name: {
                required: true,
            },

            address: {
                required: true,
            },
            phone: {
                required: true,
            },

            service: {
                required: true,
            },
            status: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            var leadId = $("#lead_id").val(); // Get ID from a hidden field or data attribute
            let apiEndpoint = "/api/lead/update/" + leadId;
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);

            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("Lead updated !", "success");
                    setTimeout(function () {
                        window.location.href = "/dashboard/leads";
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");

                    $(".error_list").remove();

                    if (xhr.status == 403) {
                        showAlert("Check the error's above", "danger");
                        var errors = xhr.responseJSON.errors;
                        $("#validation_error").addClass("error_listing");

                        $.each(errors, function (key, value) {
                            const errorDiv = $(
                                '<div class="error_list"></div>'
                            ).text(value);
                            $("#validation_error").append(errorDiv);
                        });
                    } else {
                        showAlert(
                            "System error please check later !",
                            "danger"
                        );
                    }
                },
            });
        },
    });


$(document).ready(function () {
    // Select / Deselect all
    $('#select_all').on('click', function () {
        $('.lead_checkbox').prop('checked', this.checked);
    });

    // Bulk send email button click
    $('#bulk_email_btn').on('click', function () {
        let selectedLeads = [];
        $('.lead_checkbox:checked').each(function () {
            selectedLeads.push($(this).val());
        });

        if (selectedLeads.length === 0) {
            showAlert("Please select at least one lead.", "danger");
            return;
        }

        // Open a modal for entering subject & message
        $('#bulkEmailModal').modal('show');
        $('#selected_leads').val(selectedLeads.join(',')); // Store in hidden field
    });
});

$("#bulk_email_form").submit(function(e) {
    e.preventDefault();
    let baseURL = window.location.origin;
    let apiUrl = baseURL + "/api/leads/sendbulkmail";
    let formData = $(this).serialize();
 $(".form_process_loader").removeClass("d-none");
    $.ajax({
        url: apiUrl,
        type: "POST",
        data: formData,
        dataType: "json",
        success: function(response) {
               $(".form_process_loader").addClass("d-none");
            $('#bulkEmailModal').modal('hide');
            showAlert(response.message, "success");
        },
        error: function(xhr) {
               $(".form_process_loader").addClass("d-none");
            showAlert(xhr.responseJSON?.message || "Failed to send bulk email.", "danger");
        }
    });
});



    var leadId = null;
    // Handle delete button click
    $(document).on("click", ".lead_delete_btn", function () {
        leadId = $(this).data("id");
        $("#leadDeleteModal").modal("show");
    });
    // Handle delete confirmation in modal
    $("#leadDeleteModal .delete_record").on("click", function () {
        if (leadId) {
            let baseURL = window.location.origin;
            let apiEndpoint = `/api/lead/delete/${leadId}`;
            let apiUrl = baseURL + apiEndpoint;
            $.ajax({
                url: apiUrl,
                type: "DELETE",
                headers: {
                    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                        "content"
                    ),
                },
                success: function (response) {
                    // Handle success
                    $("#leadDeleteModal").modal("hide");

                    var $row = $('a[data-id="' + leadId + '"]').closest(
                        "tr"
                    );
                    $row.append(
                        '<span class="Deleting_row"> Deleting...</span>'
                    );

                    setTimeout(function () {
                        $row.fadeOut(500, function () {
                            $(this).remove(); // Remove the row from the DOM
                        });
                        showAlert("Lead deleted successfully !", "success");
                    }, 1000); // Duration of the CSS transition
                },
                error: function (xhr) {
                    // Handle error
                    showAlert("There is a problem contact supprt !", "error");
                },
            });
        }
    });

    ////////////////// get records of states on the basis of country

    $("#country_filter").on("change", function () {
        var countryId = this.value;
        $("#state_filter_data").html('<option value="">Loading...</option>');

        if (countryId) {
            $.ajax({
                url: "/api/get-filter-states/" + countryId,
                type: "GET",
                dataType: "json",
                success: function (res) {
                    $("#state_filter_data").html(
                        '<option value="">Select State</option>'
                    );
                    $.each(res, function (key, value) {
                        $("#state_filter_data").append(
                            '<option value="' +
                            value.id +
                            '">' +
                            value.name +
                            "</option>"
                        );
                    });
                },
            });
        } else {
            $("#state_filter_data").html(
                '<option value="">Select State</option>'
            );
        }
    });

    $("#admin_filter_leaderboard").on("submit", function (e) {
        e.preventDefault();
        $.ajax({
            url: "/api/leaderboard/filter",
            method: "POST",
            data: $(this).serialize(),
            beforeSend: () =>
                $("#leaderboard_result").html("<p>Loading...</p>"),
            success: (res) => $("#leaderboard_result").html(res.html),
            error: () =>
                $("#leaderboard_result").html("<p>Error loading data</p>"),
        });
    });




});

