@extends('dashboard.auth.layout')
@section('title', 'Register')

@section('content')

<section class="auth_form_section d-block w-100 position-relative">
    <div class="auth_back_btn position-fixed">
        <a href="#" class="d-flex align-items-center justify-content-center"><i
                class="fas fa-chevron-left"></i></a>
    </div>
    <div class="auth_form_main row m-0">
        <div class="col-12 col-md-6 col-lg-6 p-0">
            <div class="auth_form_content d-grid">
                <div class="auth_form_content_box d-flex flex-column justify-content-center">
                    <div
                        class="auth_logo_box d-flex flex-column w-100 align-items-center justify-content-center text-center">
                        <small>Powered by</small>
                        <img src="/images/logo.png" alt="business logo">
                    </div>
                    <div
                        class="auth_form_heading d-flex flex-column align-items-center justify-content-center text-center w-100">
                        <h3>Create an account</h3>
                        <p>Make your credentials to continue</p>
                    </div>
                    <div id="validation_error"></div>
                    <form class="d-flex w-100" method="POST" id="auth_register" novalidate="novalidate">
                        @csrf
                        <div class="d-flex flex-column w-100">

                            <div class="auth_field_box d-grid w-100">
                                <div class="auth_field_data d-grid w-100 position-relative">
                                    <span>Full Name</span>
                                    <input type="text" placeholder="Enter your name" name="name" />
                                </div>
                                <label class="error" generated="true" for="name"></label>
                            </div>

                            <div class="auth_field_box d-grid w-100">
                                <div class="auth_field_data d-grid w-100 position-relative">
                                    <span>Business Name</span>
                                    <input type="text" placeholder="Enter your business name" name="business_name" />
                                </div>
                                <label class="error" generated="true" for="business_name"></label>
                            </div>

                            <div class="auth_field_box d-grid w-100">
                                <div class="auth_field_data d-grid w-100 position-relative">
                                    <span>Business Email Address</span>
                                    <input type="text" placeholder="Enter your email" name="email" />
                                </div>
                                <label class="error" generated="true" for="email"></label>
                            </div>

                            <div class="auth_field_box d-grid w-100">
                                <div class="auth_field_data d-grid w-100 position-relative">
                                    <span>Create Password</span>
                                    <input type="password" placeholder="Enter your Password" name="password" id="password" class="password-field" />
                                    <i class="far fa-eye-slash toggle-password"></i>
                                </div>
                                <label class="error" generated="true" for="password"></label>
                            </div>

                            <div class="auth_field_box d-grid w-100">
                                <div class="auth_field_data d-grid w-100 position-relative">
                                    <span>Confirm Password</span>
                                    <input type="password" placeholder="Enter your Password" name="password_confirmation" class="password-field" />
                                    <i class="far fa-eye-slash toggle-password"></i>
                                </div>
                                <label class="error" generated="true" for="password_confirmation"></label>
                            </div>


                            <!-- <div class="auth_forget_pass_popup d-flex w-100 justify-content-end"><a>Forgot Password ?</a>
                                </div> -->

                            <div
                                class="auth_submit_btn d-flex align-items-center justify-content-center w-100">
                                <button id="authRegisterForm" class="w-100 d-flex align-items-center justify-content-center position-relative" type="submit">Sign Up
                                    @include('dashboard/include/loader')
                                </button>

                            </div>
                        </div>
                    </form>

                    <div
                        class="auth_have_account_btn d-flex flex-column align-items-center justify-content-center w-100">
                        <p>Already have an account? <a href="{{route('login')}}">Log In</a></p>
                        <!-- <span>or</span> -->
                    </div>
                    <!-- <div class="auth_social_btn d-grid w-100">
                            <a class="d-flex align-items-center justify-content-center w-100"><img
                                    src="" alt="google image">
                                <span>Continue with Google</span></a>
                            <a class="d-flex align-items-center justify-content-center w-100">
                                <i class="fab fa-facebook-f"></i>
                                <span>Continue with Facebook</span>
                            </a>
                        </div> -->
                    <!-- <div
                            class="auth_policy_btn d-flex align-items-center justify-content-center w-100 flex-wrap text-center">
                            <p>* By signing up, you agree to our <a>Terms of Use</a> and acknowledge you’ve read our
                                <a>Privacy Policy</a>
                            </p>

                        </div> -->
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-6 p-0">
            <figure class="w-100 h-100">
                <img class="w-100 h-100 object-fit-cover" src="{{asset('/images/loginbanner.webp')}}" />
            </figure>
        </div>
    </div>
</section>

@endsection