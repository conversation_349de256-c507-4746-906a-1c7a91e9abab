@extends('dashboard.include.layout')
@section('title', 'Brands Listing')
@section('content')

<div class="redirect_button d-flex w-100 justify-content-end">
    <a href="/dashboard/brand/add">Add brand</a>
</div>
<table class="dynamic_datatable table table-striped" style="width:100%">
    <thead>
        <tr>
            <th>Status</th>
            <th>Brand Logo</th>
            <th>Brand Name</th>
            <th class="text-end">Action</th>
        </tr>
    </thead>
    <tbody>

        @foreach($brand as $brand)

        <tr>
            <td>

          
    <a href="{{ route('meta.connect') }}" class="btn btn-primary">Connect with Meta</a>



                <div class="form-check form-switch">
                    <input
                        class="form-check-input brand_status_btn"
                        type="checkbox"
                        id="flexSwitchCheckDefault{{ $brand->id }}"
                        data-id="{{ $brand->id }}"
                        {{ $brand->status == 1 ? 'checked' : '' }}>
                    <label class="form-check-label" for="flexSwitchCheckDefault"></label>
                </div>
               
            </td>

            <td>
                <div class="table_image d-flex align-items-center justify-content-center">
                    @if($brand->brand_logo)
                    <div class="file_preview_hov w-100 h-100 d-flex flex-column align-items-center position-relative">
                        <img class="w-100 h-100 object-fit-contain" src="{{$brand->brand_logo}}">
                        <button class="position-absolute top-0 left-0 w-100 h-100 d-flex align-items-center justify-content-center" onclick="openPreview('{{ $brand->brand_logo }}', 'image')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>

                    @else
                    <img class="w-100 h-100 object-fit-contain" src="/images/dummy.webp" alt="default image">
                    @endif
                </div>
            </td>
            <td>{{ $brand->brand_name }}</td>
           


            <td>
                <div class="action_filter d-flex justify-content-end">
                    <label type="button" id="action_toggle_{{ $brand->id }}" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-ellipsis-v"></i>
                    </label>
                    <div class="action_dropdown_main dropdown-menu" aria-labelledby="action_toggle_{{ $brand->id }}">
                        <ul class="action_dropdown d-grid w-100">
                            <li class="w-100">
                                <a href="{{ route('brand.update', $brand->id) }}" class="w-100 d-flex align-items-center">
                                    <i class="fas fa-edit"></i> Update
                                </a>
                            </li>
                            <li class="w-100">
                                <a data-id="{{ $brand->id }}" class="w-100 d-flex align-items-center brand_delete_btn">
                                    <i class="fas fa-trash"></i> Delete
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </td>
        </tr>

        @endforeach

    </tbody>
    <tfoot>
        <tr>
            <th>Status</th>
            <th>Brand Logo</th>
            <th>Brand Name</th>
           
            <th class="text-end">Action</th>
        </tr>
    </tfoot>
</table>

@include('dashboard/brand/delete')
@include('dashboard/brand/confirm')
@endsection