@extends('dashboard.include.layout')
@section('title', 'Update Brand')
@section('content')

<div class="section_header d-flex w-100 flex-column">
    <h3>Update Brand Info</h3>
    <div id="validation_error"></div>
</div>
<form method="POST" id="update_brand" enctype="multipart/form-data">
    @csrf
    <div class="form_field_group row">

        <input type="hidden" id="brand_id" name='brand_id' value="{{$brand->id}}">

        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Brand Name</span>
            <input type="text" placeholder="Enter name" name="brand_name" value="{{$brand->brand_name}}" />
            <label
                for="brand_name"
                generated="true"
                class="error"></label>
        </div>

 


        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <div class="parent_field form_field position-relative col-12 col-md-12 col-lg-12">
                <span>Brand Logo Preview</span>
                <figure class="d-flex w-100 h-100 image_preview_box">
                    @if($brand->brand_logo)
                    <img id="profileImagePreview" class="w-100 h-100 object-fit-contain" src="{{$brand->brand_logo}}" alt="preview image">
                    @else
                    <img id="profileImagePreview" class="w-100 h-100 object-fit-contain" src="/images/dummy.webp" alt="preview image">
                    @endif
                </figure>
                <label id="profileImageError" class="error"></label>
            </div>

            <div class="bnt_form_field parent_field d-grid">
                <div class="form_field position-relative col-12 col-md-12 col-lg-12">
                    <span>Brand Logo</span>
                    <input type="text" name="brand_logo" data-preview="profileImagePreview" data-error="profileImageError" value="{{$brand->brand_logo}}" oninput="updateImagePreview(this)" />
                    <label for="brand_logo" generated="true" class="error"></label>
                </div>
                <button class="btn cloud_upload open_gallery" data-type="galleryView"><i class="fas fa-cloud-upload"></i></button>
            </div>
        </div>


    </div>




    <div class="form_button d-flex w-100 justify-content-end">
        <button type="submit" class="w-100 d-flex align-items-center justify-content-center position-relative">Submit @include('dashboard/include/loader') </button>
    </div>
</form>

@endsection