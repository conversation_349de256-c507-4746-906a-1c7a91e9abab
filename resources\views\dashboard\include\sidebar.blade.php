<div class="dashboard_sidebar d-flex flex-column position-fixed overflow-y-auto">
    <div class="sidebar_control_btn control_btn justify-content-end mb-4 mr-4">
        <i class="sidebar_toggle_btn fa-solid fa-bars"></i>
    </div>
    <div class="sidebar_logo d-flex w-100 align-items-center justify-content-center flex-column">
        <small>Powered by</small>
        <img src="/images/logo.png">
    </div>



    <div class="filters_accordion_parent d-grid w-100">

        <ul class="filters_listing d-flex flex-column w-100 align-items-start overflow-y-auto">


            @php
            $user = auth()->user();
            @endphp


            <li class="d-flex align-items-center flex-wrap w-100 {{ Request::is('dashboard/home') ? 'active' : '' }}">
                <a href="{{ url('dashboard/home') }}" class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Dashboard</a>
            </li>


            @if($user && $user->role && ($user->role->role_key === 'admin' || $user->role->role_key === 'super_admin'))

            <li
                class="d-flex align-items-center flex-wrap w-100 {{ Request::is('dashboard/brands') ? 'active' : '' }}">
                <a href="{{ url('/dashboard/brands') }}"
                    class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Brands Management</a>
            </li>

            <!-- <li
                class="d-flex align-items-center flex-wrap w-100 {{ Request::is('/dashboard/meta-apps') ? 'active' : '' }}">
                <a href="{{ url('/dashboard/meta-apps') }}"
                    class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Meta App Management
                </a>
            </li>


            <li
                class="d-flex align-items-center flex-wrap w-100 {{ Request::is('dashboard/country') ? 'active' : '' }}">
                <a href="{{ url('/dashboard/country') }}"
                    class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Division Management</a>
            </li> -->


            @endif



            @if($user && $user->role && ($user->role->role_key === 'franchise' || $user->role->role_key === 'admin' || $user->role->role_key === 'super_admin'))
            <!-- <li
                class="d-flex align-items-center flex-wrap w-100 {{ Request::is('dashboard/projects') ? 'active' : '' }}">
                <a href="{{ url('/dashboard/projects') }}"
                    class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Project Managment</a>
            </li>

            <li class="d-flex align-items-center flex-wrap w-100 {{ Request::is('dashboard/leads') ? 'active' : '' }}">
                <a href="{{ url('dashboard/leads') }}" class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Leads Tracker</a>
            </li> -->

            @endif



            @if($user && $user->role && ($user->role->role_key === 'admin'))
            <!-- <li class="d-flex align-items-center flex-wrap w-100 {{ Request::is('dashboard/franchisee/prospects') ? 'active' : '' }}">
                <a href="{{ url('dashboard/franchisee/prospects') }}" class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Franchisee Prospects</a>
            </li> -->
            @endif

            @if($user && $user->role && ($user->role->role_key === 'super_admin'))
            <!-- <li class="d-flex align-items-center flex-wrap w-100 {{ Request::is('dashboard/media') ? 'active' : '' }}">
                <a href="{{ url('dashboard/media') }}" class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Media</a>
            </li> -->
            @endif

            @if($user && $user->role && ($user->role->role_key === 'franchise' || $user->role->role_key === 'admin' || $user->role->role_key === 'super_admin'))
            <!-- <li class="d-flex align-items-center flex-wrap w-100 {{ Request::is('dashboard/leaderboard') ? 'active' : '' }}">
                <a href="{{ url('dashboard/leaderboard') }}" class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Leaderboard</a>
            </li> -->

            @endif
        </ul>




    </div>


</div>