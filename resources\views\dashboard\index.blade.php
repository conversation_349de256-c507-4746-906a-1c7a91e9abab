@extends('dashboard.include.layout')
@section('title', 'Dashboard')
@section('content')

@php
$user = auth()->user();
@endphp

@if($user && $user->role && ($user->role->role_key === 'admin' || $user->role->role_key === 'super_admin'))

<!-- @if(!auth()->user()->business->metaConnection)
    <a href="{{ route('meta.connect') }}" class="btn btn-primary">Connect with Meta</a>
@else
    <div class="alert alert-success">Meta account connected!</div>
@endif
  -->
<!-- <form action="{{ route('meta.connect') }}" id="" enctype="multipart/form-data">
    @csrf

    <div class="form_field_group row">
        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Select app</span>
            <select name="app">
                <option selected disabled>select account</option>
                @foreach($metaAccounts as $meta)
                <option value="{{ $meta->id }}">{{ $meta->app_name }}</option>
                @endforeach
            </select>
            <label for="app" generated="true" class="error"></label>
        </div>
       

        <div class="child_field form_field position-relative col-12 col-md-12 col-lg-12">
            <div class="form_button d-flex w-100 justify-content-end"><button type="submit">Submit</button></div>
        </div>


    </div>
</form> -->



<div class="data_box row">
    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Franchisees</h5>
                <span>{{number_format($numFranchisees,2);}}</span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>
    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Projects</h5>
                <span>{{number_format($numProjects,2);}}</span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Completed Projects</h5>
                <span>{{number_format($completedNumProjects,2);}}</span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Project Revenue
                </h5>
                <span>$ {{ number_format($totalRevenue, 2); }} </span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5> Completed Projects Revenue</h5>
                <span>$ {{ number_format($completedTotalRevenue, 2); }} </span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>




</div>

<div class="heading_box_two d-flex w-100 mb-0">
    <h3>Lead Conversion Rate</h3>
</div>

<div class="data_box row">
    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Prospects</h5>
                <span>{{number_format($totalLeads,2);}}</span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>
    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Prospect Conversion Rate</h5>
                <span>{{$saleMadeRatio}} %</span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>




</div>


<div class="heading_box_two d-flex w-100">
    <h3>Franchisee Graphs</h3>
</div>

<div class="graphs row">
    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="franchiseeProjectChart" class="w-100 h-100"></canvas>
    </div>
    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="countryWiseFranchiseChart" class="w-100 h-100"></canvas>
    </div>
    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="stateWiseFranchiseChart" class="w-100 h-100"></canvas>
    </div>

</div>

<div class="heading_box_two d-flex w-100">
    <h3>Project Graphs</h3>
</div>

<div class="graphs row">

    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="countryWiseProjectChart" class="w-100 h-100"></canvas>
    </div>
    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="stateWiseProjectChart" class="w-100 h-100"></canvas>
    </div>

</div>

<div class="heading_box_two d-flex w-100">
    <h3>Revenue Graphs</h3>
</div>


<div class="graphs row">


    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="countryWiseRevenueChart" class="w-100 h-100"></canvas>
    </div>

    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="stateWiseRevenueChart" class="w-100 h-100"></canvas>
    </div>
</div>

<hr />

<div class="heading_box_two d-flex w-100">
    <h3>Search by Franchisee</h3>
</div>

<form method="POST" id="admin_filter_data" enctype="multipart/form-data">
    @csrf

    <div class="form_field_group row">
        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Select Franchise</span>
            <select name="franchise">
                <option selected disabled>select franchise</option>
                @foreach($franchisees as $franchisee)
                <option value="{{ $franchisee->id }}">{{ $franchisee->franchisee_name }}</option>
                @endforeach
            </select>
            <label for="franchise" generated="true" class="error"></label>
        </div>
        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Select Date (optional)</span>

            <input type="text" placeholder="Select date range" name="date_range" class="dateRangeInput">

            <label for="date_range" generated="true" class="error"></label>
        </div>

        <div class="child_field form_field position-relative col-12 col-md-12 col-lg-12">
            <div class="form_button d-flex w-100 justify-content-end"><button type="submit">Filter</button></div>
        </div>


    </div>
</form>


<div class="data_box row">

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Country</h5>
                <span id="franchiseeCountry"> - </span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>State</h5>
                <span id="franchiseeState"> - </span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>


    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Projects</h5>
                <span id="totalProjects"> - </span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>
    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Completed Projects</h5>
                <span id="completedProjects"> - </span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Revenue from Projects</h5>
                <span id="totalRevenue"> - </span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Completed Projects Revenue</h5>
                <span id="completedRevenue"> - </span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>
</div>


<div class="graphs row">
    <div class="single_graph col-12 col-md-6 col-lg-6 d-flex flex-column">
        <div class="heading_box_two d-flex w-100">
            <h3>Total Project Revenue
            </h3>
        </div>

        <canvas id="allProjectChart" class="w-100 h-100"></canvas>
    </div>


    <div class="single_graph col-12 col-md-6 col-lg-6 d-flex flex-column">

        <div class="heading_box_two d-flex w-100">
            <h3>Completed Projects Revenue</h3>
        </div>

        <canvas id="completedProjectChart" class="w-100 h-100"></canvas>



    </div>

</div>


<div class="heading_box_two d-flex w-100">
    <h3>Search Revenue by Date</h3>
</div>

<form method="POST" id="admin_revenue_filter_data" enctype="multipart/form-data">
    @csrf

    <div class="form_field_group row">
       
        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Select Date</span>

            <input type="text" placeholder="Select date range" name="date_range" class="dateRangeInput">

            <label for="date_range" generated="true" class="error"></label>
        </div>

        <div class="child_field form_field position-relative col-12 col-md-12 col-lg-12">
            <div class="form_button d-flex w-100 justify-content-end"><button type="submit">Filter</button></div>
        </div>


    </div>
</form>

<div class="data_box row">

  

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Projects</h5>
                <span id="revenue_totalProjects"> - </span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>
    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Completed Projects</h5>
                <span id="revenue_completedProjects"> - </span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Revenue from Projects</h5>
                <span id="project_totalRevenue"> - </span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Completed Projects Revenue</h5>
                <span id="project_completedRevenue"> - </span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>
</div>


<div class="graphs row">
    <div class="single_graph col-12 col-md-6 col-lg-6 d-flex flex-column">
        <div class="heading_box_two d-flex w-100">
            <h3>Total Project Revenue
            </h3>
        </div>

        <canvas id="allProjectRevenueChart" class="w-100 h-100"></canvas>
    </div>


    <div class="single_graph col-12 col-md-6 col-lg-6 d-flex flex-column">

        <div class="heading_box_two d-flex w-100">
            <h3>Completed Projects Revenue</h3>
        </div>

        <canvas id="completedProjectRevenueChart" class="w-100 h-100"></canvas>



    </div>

</div>


@endif

@if($user && $user->role && ($user->role->role_key === 'franchise'))



<div class="heading_box_two d-flex w-100 mb-0">
    <h3>Lead Conversion Rate</h3>
</div>

<div class="data_box row">

    <div class="single_box col-12 col-md-6 col-lg-6 ">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Lead Conversion Rate</h5>
                <span> {{ $saleMadeRatio }} % </span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>
</div>

<div class="data_box row">

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Country</h5>
                <span> {{$franchisees?->country?->name ?? 'N/A'}} </span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>State</h5>
                <span> {{$franchisees?->state?->name ?? 'N/A'}} </span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>


    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Projects</h5>
                <span> {{number_format($NumProjects,2);}} </span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>
    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Completed Projects</h5>
                <span> {{number_format($completedProjectCount,2);}} </span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>
    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Revenue from Projects</h5>
                <span>$ {{number_format($totalRevenue,2);}} </span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Completed Projects Revenue</h5>
                <span>$ {{ number_format($completedTotalRevenue, 2);}} </span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>
</div>


<div class="heading_box_two d-flex w-100">
    <h3>Revenue Graphs</h3>
</div>

<div class="graphs row">

    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="franchiseeAllProjectChart" class="w-100 h-100"></canvas>
    </div>
    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="franchiseeCompletedProjectChart" class="w-100 h-100"></canvas>
    </div>


</div>

<div class="heading_box_two d-flex w-100">
    <h3>Filter Revenue Record</h3>
</div>

<form method="POST" id="franchisee_filter_data" enctype="multipart/form-data">
    @csrf

    <div class="form_field_group row">

        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Select Date Range</span>

            <input type="text" placeholder="Select date range" name="date_range" class="dateRangeInput">

            <label for="date_range" generated="true" class="error"></label>
        </div>

        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <div class="form_button d-flex w-100 justify-content-start"><button type="submit">Filter</button></div>
        </div>


    </div>
</form>


<div class="data_box row">



    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Projects</h5>
                <span id="totalProjects"> - </span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>
    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Completed Projects</h5>
                <span id="completedProjects"> - </span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Revenue from Projects</h5>
                <span id="totalRevenue"> - </span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Completed Projects Revenue</h5>
                <span id="completedRevenue"> - </span>
            </strong>
            <img lazy="loading" src="{{asset('images/dummy.webp')}}">
        </div>
    </div>
</div>


<div class="graphs row">
    <div class="single_graph col-12 col-md-6 col-lg-6 d-flex flex-column">
        <div class="heading_box_two d-flex w-100">
            <h3>Total Project Revenue
            </h3>
        </div>

        <canvas id="allProjectChart" class="w-100 h-100"></canvas>
    </div>


    <div class="single_graph col-12 col-md-6 col-lg-6 d-flex flex-column">

        <div class="heading_box_two d-flex w-100">
            <h3>Completed Projects Revenue</h3>
        </div>

        <canvas id="completedProjectChart" class="w-100 h-100"></canvas>



    </div>

</div>





@endif




@endsection