<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Middleware\Authenticate;
use App\Http\Controllers\Api\authController;
use App\Http\Controllers\Api\businessApiController;
use App\Http\Controllers\Api\globalController;
use App\Http\Controllers\Api\countryController;
use App\Http\Controllers\Api\metaAppController;
use App\Http\Controllers\Api\franchiseeController;
use App\Http\Controllers\Api\projectController;
use App\Http\Controllers\Api\leadController;

use App\Http\Controllers\Api\MetaController;
use App\Http\Controllers\Api\brandController;

use App\Http\Controllers\Api\chartController;
use App\Http\Middleware\userRoleCheck;
/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
// Protected route example
Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
  return $request->user();
});
// Mobile routes have been moved to routes/mobile.php

    Route::get('/meta/connect', [MetaController::class, 'redirectToMeta'])->name('meta.connect');
    Route::get('/meta/callback', [MetaController::class, 'handleCallback'])->name('meta.callback');

//portal login and signup
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/update/password', [AuthController::class, 'updatePassword']);
Route::post('/logout', [AuthController::class, 'logout']);
Route::post('/forget-password', [AuthController::class, 'forgetPassword']);

Route::get('/chart/data', [ChartController::class, 'dashboardGraphData']);



// for admin
Route::get('/filter-projects', [ChartController::class, 'getFilteredProjects']);
Route::get('/projects/revenue', [ChartController::class, 'getProjectsRevenue']);

//leader board filter routes
Route::get('/get-filter-states/{id}', [globalController::class, 'getFilterStates']);
Route::post('/leaderboard/filter', [globalController::class, 'leaderboardFilter']);

// for franchisee
Route::get('/franchisee/revenue', [ChartController::class, 'getFilteredRevenue']);


Route::post('/dashboard/image/web', [globalController::class, 'uploader']);
Route::delete('/dashboard/file/delete', [globalController::class, 'deleteFile']);

//get files from the gallery
Route::get('/dashboard/gallery/files', [globalController::class, 'galleryView']);

// website settings
Route::post('/dashboard/website/settings', [globalController::class, 'webSettings']);



Route::middleware([Authenticate::class, userRoleCheck::class . ':super_admin'])->group(function () {
  //business user managment routes
  Route::post('/business/user/add', [businessApiController::class, 'addBusinessUser']);
  Route::Delete('/business/user/delete/{id}', [businessApiController::class, 'deleteBusinessUser']);
  Route::put('/business/user/status/update/{id}', [businessApiController::class, 'businessUserStatus']);
  Route::post('/business/user/update', [businessApiController::class, 'businessUserUpdate']);
});

Route::middleware([Authenticate::class, userRoleCheck::class . ':admin,super_admin'])->group(function () {
  
  //brands managment routes
  Route::post('/brand/add', [brandController::class, 'storeBrand']);
  Route::post('/brand/update/{id}', [brandController::class, 'updateBrand']);
  Route::Delete('/brand/delete/{id}', [brandController::class, 'deleteBrand']);
  Route::post('/brand/status/update/{id}', [brandController::class, 'BrandStatus']);

});
Route::middleware([Authenticate::class, userRoleCheck::class . ':admin,super_admin,franchise'])->group(function () {

  //project managment routes
  Route::post('/project/add', [projectController::class, 'storeProject']);
  Route::post('/project/update/{id}', [projectController::class, 'updateProject']);
  Route::Delete('/project/delete/{id}', [projectController::class, 'deleteProject']);

  //project Status approval routes
  Route::post('/project/status/update/{id}', [projectController::class, 'ProjectStatus']);


    //leads managment routes
  Route::post('/lead/add', [leadController::class, 'storeLead']);
  Route::post('/lead/update/{id}', [leadController::class, 'updateLead']);
  Route::Delete('/lead/delete/{id}', [leadController::class, 'deleteLead']);

  Route::post('/leads/sendbulkmail', [LeadController::class, 'sendBulkMail']);
});
