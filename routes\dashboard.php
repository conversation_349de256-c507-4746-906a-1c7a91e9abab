<?php

use App\Http\Controllers\Dashboard\homeController;
use App\Http\Controllers\Api\MetaController;
use App\Http\Middleware\Authenticate;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\View;
use App\Http\Middleware\userRoleCheck;

Route::get('/privacy-policy', function () {
    return view('dashboard.privacyPolicy');
});
Route::get('/terms-and-conditions', function () {
    return view('dashboard.termsAndConditions');
});


Route::get('/portal/login', [homeController::class, 'loginView'])->name('login');
Route::get('/portal/signup', [homeController::class, 'signupView'])->name('signup');
Route::get('/portal/forget-password', [homeController::class, 'forgetPasswordView'])->name('forget.password');
Route::get('/portal/password/reset/{token}', [homeController::class, 'showResetForm'])->name('password.reset');

// Meta OAuth callback route (public route)
Route::get('/meta/callback', [MetaController::class, 'handleCallback'])->name('meta.callback');

Route::middleware([Authenticate::class])->group(function () {

  Route::get('/', [homeController::class, 'dashboardView']);
  Route::get('/dashboard/home', [homeController::class, 'dashboardView'])->name('dashboard.home');
    Route::get('/dashboard/asset', [homeController::class, 'assetView']);
  //Route::get('/dashboard/media', [homeController::class, 'mediaView']);
  //Route::get('/dashboard/leaderboard', [homeController::class, 'leaderboardView']);

  //website setting page routes
 // Route::get('/dashboard/website/setting', [homeController::class, 'webSettingView'])->name('website.setting');
});

Route::middleware([Authenticate::class, userRoleCheck::class . ':super_admin,admin'])->group(function () {
  Route::get('/dashboard/profile', [homeController::class, 'profileView'])->name('business.profile');
  Route::get('/dashboard/business/user/update/{id}', [homeController::class, 'businessUserUpdateView'])->name('business.user.update');
});

Route::middleware([Authenticate::class,userRoleCheck::class . ':admin,super_admin'])->group(function () {

  // brands Routes
  Route::get('/dashboard/brands', [homeController::class, 'brandView']);
  Route::get('/dashboard/brand/add', [homeController::class, 'addbrandView']);
  Route::get('/dashboard/brand/update/{id}', [homeController::class, 'brandUpdateView'])->name('brand.update');



});




