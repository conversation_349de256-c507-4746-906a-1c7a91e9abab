<div class="offcanvas offcanvas-end" tabindex="-1" id="addUser" aria-labelledby="addUserLabel">
    <div class="offcanvas-header">
        <h5 id="addUserLabel">User Managment</h5>
        <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
        <form id="add_user" method="POST" enctype="multipart/form-data">
            <?php echo csrf_field(); ?>
            <div class="parent_field form_field position-relative col-12 col-md-12 col-lg-12">
                <span>User Name</span>
                <input type="text" placeholder="Enter name" name="name">
                <label for="name" generated="true" class="error"></label>
            </div>
            <div class="parent_field form_field position-relative col-12 col-md-12 col-lg-12">
                <span>User Email</span>
                <input type="email" placeholder="Enter email" name="email">
                <label for="email" generated="true" class="error"></label>
            </div>
            <div class="parent_field form_field position-relative col-12 col-md-12 col-lg-12">
                <span>Password</span>
                <input type="password" placeholder="Enter password" name="password">
                <label for="password" generated="true" class="error"></label>
            </div>
            <div class="parent_field form_field position-relative col-12 col-md-12 col-lg-12">
                <span>Role</span>
                <select name="role">
                    <option selected disabled>Select Role</option>
                    <?php $__currentLoopData = $role_detail; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $roles): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($roles->id); ?>"><?php echo e($roles->role); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <label for="role" generated="true" class="error"></label>
            </div>

            <div class="form_button d-flex w-100 justify-content-end">
                <button type="submit" class="w-100 d-flex align-items-center justify-content-center position-relative">Submit <?php echo $__env->make('dashboard/include/loader', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?> </button>
            </div>
        </form>
    </div>
</div><?php /**PATH D:\clients project\SocialAnalyticsCMS\resources\views/dashboard/businessProfile/user/add.blade.php ENDPATH**/ ?>