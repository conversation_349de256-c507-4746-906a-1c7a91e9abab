
<?php $__env->startSection('title', 'Website Settings'); ?>
<?php $__env->startSection('content'); ?>

<div class="section_header d-flex w-100">
    <h3>Website Settings</h3>
</div>

<form method="POST" id="web_settings" enctype="multipart/form-data">
    <?php echo csrf_field(); ?>

    <div class="form_field_group row">
        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <div class="parent_field form_field position-relative col-12 col-md-12 col-lg-12">
                <span>Website Logo</span>
                <figure class="d-flex w-100 h-100 image_preview_box">
                    <img id="coverImagePreview" class="w-100 h-100 object-fit-contain"
                        src="<?php echo e(!empty($logos->logo) ? $logos->logo : '/images/dummy.webp'); ?>" alt="preview image">
                </figure>
                <label id="coverImageError" class="error"></label>
            </div>
            <div class="bnt_form_field parent_field d-grid">
                <div class="form_field position-relative col-12 col-md-12 col-lg-12">
                    <span>Logo Image URL</span>
                    <input type="text" name="website_logo" value="<?php echo e($logos->logo ?? ''); ?>"
                        data-preview="coverImagePreview" data-error="coverImageError" oninput="updateImagePreview(this)">
                    <label for="website_logo" generated="true" class="error"></label>
                </div>
                <button class="btn cloud_upload open_gallery" data-type="galleryView"><i class="fas fa-cloud-upload"></i></button>
            </div>
        </div>
        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <div class="parent_field form_field position-relative col-12 col-md-12 col-lg-12">
                <span>Favicon</span>
                <figure class="d-flex w-100 h-100 image_preview_box">
                    <img id="faviconPreview" class="w-100 h-100 object-fit-contain"
                        src="<?php echo e(!empty($logos->favicon) ? $logos->favicon : '/images/dummy.webp'); ?>" alt="preview image">
                </figure>
                <label id="faviconError" class="error"></label>
            </div>
            <div class="bnt_form_field parent_field d-grid">
                <div class="form_field position-relative col-12 col-md-12 col-lg-12">
                    <span>Favicon</span>
                    <input type="text" name="favicon" value="<?php echo e($logos->favicon ?? ''); ?>"
                        data-preview="faviconPreview" data-error="faviconError" oninput="updateImagePreview(this)">
                    <label for="favicon" generated="true" class="error"></label>
                </div>
                <button class="btn cloud_upload open_gallery" data-type="galleryView"><i class="fas fa-cloud-upload"></i></button>
            </div>
        </div>
    </div>

    <div class="form_field_group row">

        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Contact Emails</span>
            <input class="tagify" name="contact_email" value="<?php echo e($contact_email); ?>" />
            <label for="contact_email" generated="true" class="error"></label>
        </div>
        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Newsletter Emails</span>
            <input class="tagify" name="newsletter_email" value="<?php echo e($newsletter_email); ?>" />
            <label for="newsletter_email" generated="true" class="error"></label>
        </div>
    </div>

    <div class="form_button d-flex w-100 justify-content-end">
        <button type="submit" class="w-100 d-flex align-items-center justify-content-center position-relative">
            Submit
            <?php echo $__env->make('dashboard/include/loader', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </button>
    </div>
</form>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('dashboard.include.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\clients project\SocialAnalyticsCMS\resources\views/dashboard/webSettings/index.blade.php ENDPATH**/ ?>