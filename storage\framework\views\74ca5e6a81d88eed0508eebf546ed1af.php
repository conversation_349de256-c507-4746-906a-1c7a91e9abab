
<?php $__env->startSection('title', 'Leads'); ?>
<?php $__env->startSection('content'); ?>

<?php
$user = auth()->user();
?>

<?php if($user && $user->role && ($user->role->role_key === 'franchise')): ?>
<div class="redirect_button d-flex w-100 justify-content-end gap-3">
    <!-- <a id="bulk_email_btn" href="javascript:void(0)">Send Email</a> -->
    <a href="/dashboard/leads/add">Add Lead</a></div>
<?php endif; ?>
<table class="dynamic_datatable table table-striped" style="width:100%">
    <thead>
        <tr>
            <th>
                <input type="checkbox" id="select_all">
            </th>
            <th>Status</th>
            <?php if($user && $user->role && ($user->role->role_key === 'admin' || $user->role->role_key === 'super_admin')): ?>
            <th>Franchisee</th>
            <?php endif; ?>
            <th>First Name</th>
            <th>Last Name</th>
            <th>Address</th>
            <th>Phone</th>
            <th>Email</th>
            <th>Service</th>
            <th>Notes</th>
            <th>Created At</th>
            <?php if($user && $user->role && ($user->role->role_key === 'franchise')): ?>
            <th class="text-end">Action</th>
            <?php endif; ?>
        </tr>
    </thead>
    <tbody>

        <?php $__currentLoopData = $leads; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lead): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

        <tr>
            <?php
            $statusColors = [
            'contacted, no answer' => '#fd7e14', // Orange
            'appointment booked' => '#0d6efd', // Blue
            'unsuccessful lead' => '#dc3545', // Red
            'sale made' => '#198754', // Green
            'follow-up required' => '#ffc107', // Yellow
            'NDIS Bathroom Renovation' => '#6c757d', // Grey (you can change this if needed)
            ];

            $circleColor = $statusColors[$lead->status] ?? '#999';
            ?>

            <td>
                <input type="checkbox" class="lead_checkbox" value="<?php echo e($lead->id); ?>">
            </td>
            <td>
                <span class="rounded-circle" <?php echo 'style="width:20px; height:20px; display:inline-block; background-color:' . $circleColor . ';"'; ?>></span>
            </td>

            <?php if($user && $user->role && ($user->role->role_key === 'admin' || $user->role->role_key === 'super_admin')): ?>
            <td><?php echo e($lead->franchisee->franchisee_name); ?></td>
            <?php endif; ?>


            <td><?php echo e($lead->first_name); ?></td>
            <td><?php echo e($lead->last_name ? $lead->last_name : '-'); ?></td>
            <td><?php echo e($lead->address); ?></td>
            <td><?php echo e($lead->phone); ?></td>
            <td><?php echo e($lead->email ? $lead->email : '-'); ?></td>
            <td><?php echo e($lead->service); ?></td>

            <td>
                <?php if($lead->notes): ?>
                <?php
                $shortNote = Str::limit($lead->notes, 50); // Shorten to 50 characters
                ?>

                <span class="short-note"><?php echo e($shortNote); ?></span>
                <span class="full-note d-none"><?php echo e($lead->notes); ?></span>

                <?php if(strlen($lead->notes) > 50): ?>
                <button type="button" class="btn btn-link p-0 m-0 read-toggle" style="font-size: 0.875rem;">Read more</button>
                <?php endif; ?>
                <?php else: ?>
                -
                <?php endif; ?>
            </td>

            <td><?php echo e(\Carbon\Carbon::parse($lead->created_at)->format('d F Y')); ?></td>

            <?php if($user && $user->role && ($user->role->role_key === 'franchise')): ?>
            <td>
                <div class="action_filter d-flex justify-content-end">
                    <label type="button" id="action_toggle_<?php echo e($lead->id); ?>" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-ellipsis-v"></i>
                    </label>
                    <div class="action_dropdown_main dropdown-menu" aria-labelledby="action_toggle_<?php echo e($lead->id); ?>">
                        <ul class="action_dropdown d-grid w-100">
                          
                            <li class="w-100">
                                <a href="<?php echo e(route('leads.update', $lead->id)); ?>" class="w-100 d-flex align-items-center">
                                    <i class="fas fa-edit"></i> Update
                                </a>
                            </li>
                            <li class="w-100">
                                <a data-id="<?php echo e($lead->id); ?>" class="w-100 d-flex align-items-center lead_delete_btn">
                                    <i class="fas fa-trash"></i> Delete
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </td>
            <?php endif; ?>
        </tr>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    </tbody>
    <!-- <tfoot>
        <tr>
            <th>Status</th>
            <?php if($user && $user->role && ($user->role->role_key === 'admin' || $user->role->role_key === 'super_admin')): ?>
            <th>Franchisee</th>
            <?php endif; ?>
            <th>First Name</th>
            <th>Last Name</th>
            <th>Address</th>
            <th>Phone</th>
            <th>Email</th>
            <th>Service</th>
            <th>Notes</th>
            <th>Created At</th>
            <?php if($user && $user->role && ($user->role->role_key === 'franchise')): ?> <th class="text-end">Action</th>
            <?php endif; ?>
        </tr>
    </tfoot> -->
</table>

<?php echo $__env->make('dashboard/leads/delete', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('dashboard/leads/bulkEmail', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


<?php $__env->stopSection(); ?>
<?php echo $__env->make('dashboard.include.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\clients project\SocialAnalyticsCMS\resources\views/dashboard/leads/index.blade.php ENDPATH**/ ?>