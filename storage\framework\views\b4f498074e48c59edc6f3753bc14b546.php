<?php $__env->startSection('title', 'profile'); ?>
<?php $__env->startSection('content'); ?>



    <div class="section_header d-flex w-100 justify-content-between align-items-center">
        <h3>Profile Info</h3>
    </div>
    <div class="form_field_group row">
        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Business Name</span>
            <input type="text" placeholder="Enter business name" name="business_name" value="<?php echo e($business_detail->business_name ?? ''); ?>" readonly>
            <label for="business_name" generated="true" class="error"></label>
        </div>
        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>User Name</span>
            <input type="text" placeholder="Enter user name" name="name" value="<?php echo e($user_detail->name ?? ''); ?>" readonly>
            <label for="name" generated="true" class="error"></label>
        </div>
        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Business Email</span>
            <input type="text" name="email" value="<?php echo e($user_detail->email ?? ''); ?>" readonly>
            <label for="email" generated="true" class="error"></label>
        </div>
    </div>

    <div class="section_header d-flex w-100 justify-content-between align-items-center">
        <h3>User Info</h3>
        <!-- <a data-bs-toggle="offcanvas" data-bs-target="#addUser" aria-controls="offcanvasRight">Add User</a> -->
    </div>

    <table class="dynamic_datatable table table-striped" style="width:100%">
    <thead>
        <tr>
            <th>Status</th>
            <th>Name</th>
            <th>Email</th>
            
            <th>Role</th>
             <th class="text-end">Action</th> 
        </tr>
    </thead>
    <tbody>
      

    <?php $__currentLoopData = $business_users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $users): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <tr>
            <td>
                <div class="form-check form-switch">
                    <input class="form-check-input business_user_status" type="checkbox" id="flexSwitchCheckDefault" data-id="<?php echo e($users->id); ?>" <?php echo e($users->status==1 ? "checked" : ""); ?> />
                    <label class="form-check-label" for="flexSwitchCheckDefault"></label>
                </div>
            </td>
            <td><?php echo e($users->name); ?></td>
            <td><?php echo e($users->email); ?></td>
            <td><?php echo e($users->role->role); ?></td>
             <td>
                <div class="action_filter d-flex justify-content-end">
                    <label type="button" id="action_toggle_<?php echo e($users->id); ?>" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-ellipsis-v"></i>
                    </label>
                    <div class="action_dropdown_main dropdown-menu" aria-labelledby="action_toggle_<?php echo e($users->id); ?>">
                        <ul class="action_dropdown d-grid w-100">
                            <li class="w-100">
                                <a href="<?php echo e(route('business.user.update', $users->id)); ?>" class="w-100 d-flex align-items-center">
                                    <i class="fas fa-edit"></i> Update
                                </a>
                            </li>
                            <!-- <li class="w-100">
                                <a data-id="<?php echo e($users->id); ?>" class="w-100 d-flex align-items-center business_user_delete_btn">
                                    <i class="fas fa-trash"></i> Delete
                                </a>
                            </li> -->
                        </ul>
                    </div>
                </div>
            </td> 
        </tr>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    </tbody>
    <tfoot>
        <tr>
        <th>Status</th>
            <th>Name</th>
            <th>Email</th>
          
            <th>Role</th>
         <th class="text-end">Action</th> 
        </tr>
    </tfoot>
</table>

<?php echo $__env->make('dashboard/businessProfile/user/add', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('dashboard/businessProfile/user/delete', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('dashboard.include.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\clients project\SocialAnalyticsCMS\resources\views/dashboard/businessProfile/index.blade.php ENDPATH**/ ?>