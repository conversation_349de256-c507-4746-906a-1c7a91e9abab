<?php $__env->startSection('title', 'Brands Listing'); ?>
<?php $__env->startSection('content'); ?>

<div class="redirect_button d-flex w-100 justify-content-end">
    <a href="/dashboard/brand/add">Add brand</a>
</div>
<table class="dynamic_datatable table table-striped" style="width:100%">
    <thead>
        <tr>
            <th>Status</th>
            <th>Brand Logo</th>
            <th>Brand Name</th>
            <th class="text-end">Action</th>
        </tr>
    </thead>
    <tbody>

        <?php $__currentLoopData = $brand; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

        <tr>
            <td>

          
    <a href="<?php echo e(route('meta.connect')); ?>" class="btn btn-primary">Connect with Meta</a>



                <div class="form-check form-switch">
                    <input
                        class="form-check-input brand_status_btn"
                        type="checkbox"
                        id="flexSwitchCheckDefault<?php echo e($brand->id); ?>"
                        data-id="<?php echo e($brand->id); ?>"
                        <?php echo e($brand->status == 1 ? 'checked' : ''); ?>>
                    <label class="form-check-label" for="flexSwitchCheckDefault"></label>
                </div>
               
            </td>

            <td>
                <div class="table_image d-flex align-items-center justify-content-center">
                    <?php if($brand->brand_logo): ?>
                    <div class="file_preview_hov w-100 h-100 d-flex flex-column align-items-center position-relative">
                        <img class="w-100 h-100 object-fit-contain" src="<?php echo e($brand->brand_logo); ?>">
                        <button class="position-absolute top-0 left-0 w-100 h-100 d-flex align-items-center justify-content-center" onclick="openPreview('<?php echo e($brand->brand_logo); ?>', 'image')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>

                    <?php else: ?>
                    <img class="w-100 h-100 object-fit-contain" src="/images/dummy.webp" alt="default image">
                    <?php endif; ?>
                </div>
            </td>
            <td><?php echo e($brand->brand_name); ?></td>
           


            <td>
                <div class="action_filter d-flex justify-content-end">
                    <label type="button" id="action_toggle_<?php echo e($brand->id); ?>" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-ellipsis-v"></i>
                    </label>
                    <div class="action_dropdown_main dropdown-menu" aria-labelledby="action_toggle_<?php echo e($brand->id); ?>">
                        <ul class="action_dropdown d-grid w-100">
                            <li class="w-100">
                                <a href="<?php echo e(route('brand.update', $brand->id)); ?>" class="w-100 d-flex align-items-center">
                                    <i class="fas fa-edit"></i> Update
                                </a>
                            </li>
                            <li class="w-100">
                                <a data-id="<?php echo e($brand->id); ?>" class="w-100 d-flex align-items-center brand_delete_btn">
                                    <i class="fas fa-trash"></i> Delete
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </td>
        </tr>

        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    </tbody>
    <tfoot>
        <tr>
            <th>Status</th>
            <th>Brand Logo</th>
            <th>Brand Name</th>
           
            <th class="text-end">Action</th>
        </tr>
    </tfoot>
</table>

<?php echo $__env->make('dashboard/brand/delete', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('dashboard/brand/confirm', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('dashboard.include.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\clients project\SocialAnalyticsCMS\resources\views/dashboard/brand/index.blade.php ENDPATH**/ ?>